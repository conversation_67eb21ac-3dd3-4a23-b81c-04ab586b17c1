<?php
/**
 * Vendor Authentication Middleware
 * Handles vendor session validation, auto-login, and security checks
 */

require_once __DIR__ . '/../config/database.php';

class VendorAuth {
    private $db;
    private $session_timeout = 7200; // 2 hours
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        
        // Start session if not already started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // Check for remember me token if not logged in
        if (!$this->isLoggedIn()) {
            $this->checkRememberToken();
        }
        
        // Validate existing session
        if ($this->isLoggedIn()) {
            $this->validateSession();
        }
    }
    
    /**
     * Check if user is logged in
     */
    public function isLoggedIn() {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }
    
    /**
     * Check if user is a vendor
     */
    public function isVendor() {
        return $this->isLoggedIn() && $_SESSION['user_type'] === 'vendor';
    }
    
    /**
     * Check if vendor is verified
     */
    public function isVerifiedVendor() {
        return $this->isVendor() && 
               isset($_SESSION['verification_status']) && 
               $_SESSION['verification_status'] === 'verified';
    }
    
    /**
     * Require vendor authentication
     */
    public function requireVendorAuth($redirect_to_login = true) {
        if (!$this->isVendor()) {
            if ($redirect_to_login) {
                $this->redirectToLogin();
            }
            return false;
        }
        return true;
    }
    
    /**
     * Require verified vendor
     */
    public function requireVerifiedVendor($redirect_to_verification = true) {
        if (!$this->requireVendorAuth()) {
            return false;
        }
        
        if (!$this->isVerifiedVendor()) {
            if ($redirect_to_verification) {
                $this->redirectToVerification();
            }
            return false;
        }
        return true;
    }
    
    /**
     * Get current user data
     */
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        return [
            'id' => $_SESSION['user_id'],
            'username' => $_SESSION['username'] ?? '',
            'email' => $_SESSION['email'] ?? '',
            'first_name' => $_SESSION['first_name'] ?? '',
            'last_name' => $_SESSION['last_name'] ?? '',
            'user_type' => $_SESSION['user_type'] ?? '',
            'phone' => $_SESSION['phone'] ?? '',
            'business_name' => $_SESSION['business_name'] ?? '',
            'verification_status' => $_SESSION['verification_status'] ?? '',
            'business_phone' => $_SESSION['business_phone'] ?? '',
            'business_email' => $_SESSION['business_email'] ?? ''
        ];
    }
    
    /**
     * Get vendor ID
     */
    public function getVendorId() {
        return $this->isVendor() ? $_SESSION['user_id'] : null;
    }
    
    /**
     * Update session activity
     */
    public function updateActivity() {
        if ($this->isLoggedIn()) {
            $_SESSION['last_activity'] = time();
        }
    }
    
    /**
     * Validate current session
     */
    private function validateSession() {
        // Check session timeout
        if (isset($_SESSION['last_activity'])) {
            if (time() - $_SESSION['last_activity'] > $this->session_timeout) {
                $this->logout();
                return false;
            }
        }
        
        // Update last activity
        $this->updateActivity();
        
        // Verify user still exists and is active
        if (!$this->verifyUserStatus()) {
            $this->logout();
            return false;
        }
        
        return true;
    }
    
    /**
     * Verify user status in database
     */
    private function verifyUserStatus() {
        try {
            $query = "
                SELECT u.status, vp.verification_status
                FROM users u
                LEFT JOIN vendor_profiles vp ON u.id = vp.user_id
                WHERE u.id = :user_id
            ";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $_SESSION['user_id']);
            $stmt->execute();
            
            $user = $stmt->fetch();
            
            if (!$user || $user['status'] !== 'active') {
                return false;
            }
            
            // Update verification status if it changed
            if ($this->isVendor() && isset($user['verification_status'])) {
                $_SESSION['verification_status'] = $user['verification_status'];
            }
            
            return true;
        } catch (Exception $e) {
            error_log('User status verification failed: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Check remember me token
     */
    private function checkRememberToken() {
        if (!isset($_COOKIE['remember_token'])) {
            return false;
        }
        
        try {
            $token = $_COOKIE['remember_token'];
            $hashed_token = hash('sha256', $token);
            
            $query = "
                SELECT rt.user_id, u.*, vp.business_name, vp.verification_status, vp.business_phone, vp.business_email
                FROM remember_tokens rt
                JOIN users u ON rt.user_id = u.id
                LEFT JOIN vendor_profiles vp ON u.id = vp.user_id
                WHERE rt.token = :token 
                AND rt.expires_at > CURRENT_TIMESTAMP
                AND u.status = 'active'
            ";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':token', $hashed_token);
            $stmt->execute();
            
            $user = $stmt->fetch();
            
            if ($user) {
                // Auto-login user
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['first_name'] = $user['first_name'];
                $_SESSION['last_name'] = $user['last_name'];
                $_SESSION['user_type'] = $user['user_type'];
                $_SESSION['phone'] = $user['phone'];
                $_SESSION['login_time'] = time();
                $_SESSION['last_activity'] = time();
                $_SESSION['auto_login'] = true;
                
                // Store vendor-specific data
                if ($user['user_type'] === 'vendor') {
                    $_SESSION['business_name'] = $user['business_name'];
                    $_SESSION['verification_status'] = $user['verification_status'];
                    $_SESSION['business_phone'] = $user['business_phone'];
                    $_SESSION['business_email'] = $user['business_email'];
                }
                
                return true;
            }
        } catch (Exception $e) {
            error_log('Remember token check failed: ' . $e->getMessage());
        }
        
        return false;
    }
    
    /**
     * Logout user
     */
    public function logout() {
        // Clear remember token
        if (isset($_COOKIE['remember_token'])) {
            try {
                $token = $_COOKIE['remember_token'];
                $hashed_token = hash('sha256', $token);
                
                $query = "DELETE FROM remember_tokens WHERE token = :token";
                $stmt = $this->db->prepare($query);
                $stmt->bindParam(':token', $hashed_token);
                $stmt->execute();
            } catch (Exception $e) {
                error_log('Remember token cleanup failed: ' . $e->getMessage());
            }
            
            setcookie('remember_token', '', time() - 3600, '/', '', false, true);
        }
        
        // Clear session
        $_SESSION = array();
        
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        session_destroy();
    }
    
    /**
     * Redirect to login page
     */
    private function redirectToLogin() {
        $current_url = $_SERVER['REQUEST_URI'];
        $login_url = BASE_URL . 'login';
        
        if (!empty($current_url) && $current_url !== '/') {
            $login_url .= '?redirect=' . urlencode($current_url);
        }
        
        header('Location: ' . $login_url);
        exit;
    }
    
    /**
     * Redirect to verification page
     */
    private function redirectToVerification() {
        header('Location: ' . BASE_URL . 'vendor-verification');
        exit;
    }
    
    /**
     * Check for brute force attacks
     */
    public function checkBruteForce($email, $ip_address) {
        try {
            $query = "
                SELECT COUNT(*) as failed_attempts
                FROM login_attempts
                WHERE (email = :email OR ip_address = :ip_address)
                AND success = 0
                AND attempted_at > DATE_SUB(NOW(), INTERVAL 15 MINUTE)
            ";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':email', $email);
            $stmt->bindParam(':ip_address', $ip_address);
            $stmt->execute();
            
            $result = $stmt->fetch();
            $failed_attempts = $result['failed_attempts'] ?? 0;
            
            // Block after 5 failed attempts in 15 minutes
            return $failed_attempts >= 5;
        } catch (Exception $e) {
            error_log('Brute force check failed: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Generate CSRF token
     */
    public function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Verify CSRF token
     */
    public function verifyCSRFToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
}

// Global vendor auth instance
$vendorAuth = new VendorAuth();
?>
