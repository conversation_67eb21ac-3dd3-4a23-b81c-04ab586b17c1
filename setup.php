<?php
/**
 * Musanze Marketplace Setup Script
 * Run this script once to set up the platform
 */

// Prevent running in production
if ($_SERVER['HTTP_HOST'] !== 'localhost' && $_SERVER['HTTP_HOST'] !== '127.0.0.1') {
    die('Setup script can only be run on localhost');
}

require_once 'config/database.php';

$setup_complete = false;
$errors = [];
$success_messages = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'test_connection':
            testDatabaseConnection();
            break;
        case 'create_database':
            createDatabase();
            break;
        case 'import_schema':
            importSchema();
            break;
        case 'create_demo_data':
            createDemoData();
            break;
        case 'complete_setup':
            completeSetup();
            break;
    }
}

function testDatabaseConnection() {
    global $errors, $success_messages;
    
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        if ($db) {
            $success_messages[] = "Database connection successful!";
        } else {
            $errors[] = "Failed to connect to database";
        }
    } catch (Exception $e) {
        $errors[] = "Database connection error: " . $e->getMessage();
    }
}

function createDatabase() {
    global $errors, $success_messages;
    
    try {
        // Connect without specifying database
        $pdo = new PDO(
            "mysql:host=localhost",
            'root',
            '',
            array(PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION)
        );
        
        $pdo->exec("CREATE DATABASE IF NOT EXISTS musanze_marketplace");
        $success_messages[] = "Database 'musanze_marketplace' created successfully!";
        
    } catch (Exception $e) {
        $errors[] = "Error creating database: " . $e->getMessage();
    }
}

function importSchema() {
    global $errors, $success_messages;
    
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        $schema = file_get_contents('database/schema.sql');
        
        if (!$schema) {
            $errors[] = "Could not read schema.sql file";
            return;
        }
        
        // Split by semicolon and execute each statement
        $statements = array_filter(array_map('trim', explode(';', $schema)));
        
        foreach ($statements as $statement) {
            if (!empty($statement)) {
                $db->exec($statement);
            }
        }
        
        $success_messages[] = "Database schema imported successfully!";
        
    } catch (Exception $e) {
        $errors[] = "Error importing schema: " . $e->getMessage();
    }
}

function createDemoData() {
    global $errors, $success_messages;
    
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        // Create demo users
        $demo_users = [
            [
                'username' => 'customer_demo',
                'email' => '<EMAIL>',
                'password' => password_hash('demo123', PASSWORD_DEFAULT),
                'first_name' => 'John',
                'last_name' => 'Customer',
                'phone' => '+250788123456',
                'user_type' => 'customer',
                'status' => 'active'
            ],
            [
                'username' => 'vendor_demo',
                'email' => '<EMAIL>',
                'password' => password_hash('demo123', PASSWORD_DEFAULT),
                'first_name' => 'Jane',
                'last_name' => 'Vendor',
                'phone' => '+250788654321',
                'user_type' => 'vendor',
                'status' => 'active'
            ]
        ];
        
        foreach ($demo_users as $user) {
            $stmt = $db->prepare("
                INSERT IGNORE INTO users (username, email, password, first_name, last_name, phone, user_type, status, email_verified)
                VALUES (:username, :email, :password, :first_name, :last_name, :phone, :user_type, :status, 1)
            ");
            $stmt->execute($user);
        }
        
        // Create demo vendor profile
        $vendor_id = $db->lastInsertId();
        if ($vendor_id) {
            $stmt = $db->prepare("
                INSERT IGNORE INTO vendor_profiles (user_id, business_name, business_description, verification_status)
                VALUES (:user_id, :business_name, :business_description, 'verified')
            ");
            $stmt->execute([
                'user_id' => $vendor_id,
                'business_name' => 'Demo Electronics Store',
                'business_description' => 'Your trusted electronics vendor in Musanze'
            ]);
        }
        
        // Create demo products
        $demo_products = [
            [
                'vendor_id' => $vendor_id,
                'category_id' => 1, // Electronics
                'name' => 'Samsung Galaxy A54',
                'slug' => 'samsung-galaxy-a54',
                'description' => 'Latest Samsung smartphone with excellent camera and performance',
                'short_description' => 'Samsung Galaxy A54 - 128GB Storage, 6GB RAM',
                'sku' => 'SAM-A54-128',
                'price' => 450000,
                'compare_price' => 500000,
                'stock_quantity' => 15,
                'status' => 'active',
                'featured' => 1
            ],
            [
                'vendor_id' => $vendor_id,
                'category_id' => 1, // Electronics
                'name' => 'HP Laptop 15-inch',
                'slug' => 'hp-laptop-15-inch',
                'description' => 'Reliable HP laptop perfect for work and study',
                'short_description' => 'HP Laptop - Intel i5, 8GB RAM, 256GB SSD',
                'sku' => 'HP-LAP-15',
                'price' => 650000,
                'stock_quantity' => 8,
                'status' => 'active',
                'featured' => 1
            ],
            [
                'vendor_id' => $vendor_id,
                'category_id' => 1, // Electronics
                'name' => 'Wireless Bluetooth Headphones',
                'slug' => 'wireless-bluetooth-headphones',
                'description' => 'High-quality wireless headphones with noise cancellation',
                'short_description' => 'Bluetooth Headphones - Noise Cancelling, 20hr Battery',
                'sku' => 'BT-HEAD-001',
                'price' => 45000,
                'compare_price' => 55000,
                'stock_quantity' => 25,
                'status' => 'active',
                'featured' => 0
            ]
        ];
        
        foreach ($demo_products as $product) {
            $stmt = $db->prepare("
                INSERT IGNORE INTO products 
                (vendor_id, category_id, name, slug, description, short_description, sku, price, compare_price, stock_quantity, status, featured)
                VALUES 
                (:vendor_id, :category_id, :name, :slug, :description, :short_description, :sku, :price, :compare_price, :stock_quantity, :status, :featured)
            ");
            $stmt->execute($product);
        }
        
        $success_messages[] = "Demo data created successfully!";
        
    } catch (Exception $e) {
        $errors[] = "Error creating demo data: " . $e->getMessage();
    }
}

function completeSetup() {
    global $setup_complete, $success_messages;
    
    // Create uploads directory
    if (!file_exists('uploads')) {
        mkdir('uploads', 0755, true);
    }
    
    // Create subdirectories
    $upload_dirs = ['products', 'users', 'vendors'];
    foreach ($upload_dirs as $dir) {
        if (!file_exists("uploads/$dir")) {
            mkdir("uploads/$dir", 0755, true);
        }
    }
    
    $setup_complete = true;
    $success_messages[] = "Setup completed successfully! You can now use the platform.";
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Musanze Marketplace - Setup</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body style="background: var(--gray-light);">
    <div class="container" style="padding: 2rem 0; max-width: 800px;">
        <div class="card">
            <div class="card-header text-center">
                <h2><i class="fas fa-store" style="color: var(--secondary-green);"></i> Musanze Marketplace</h2>
                <p style="color: var(--gray-dark); margin: 0;">Platform Setup Wizard</p>
            </div>
            
            <div class="card-body">
                <?php if (!empty($errors)): ?>
                    <div style="background: #ffebee; border: 1px solid #f44336; border-radius: var(--border-radius); padding: 1rem; margin-bottom: 1rem;">
                        <h5 style="color: #f44336; margin-bottom: 0.5rem;"><i class="fas fa-exclamation-circle"></i> Errors</h5>
                        <ul style="margin: 0; color: #f44336;">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($success_messages)): ?>
                    <div style="background: #e8f5e8; border: 1px solid #4CAF50; border-radius: var(--border-radius); padding: 1rem; margin-bottom: 1rem;">
                        <h5 style="color: #4CAF50; margin-bottom: 0.5rem;"><i class="fas fa-check-circle"></i> Success</h5>
                        <ul style="margin: 0; color: #4CAF50;">
                            <?php foreach ($success_messages as $message): ?>
                                <li><?php echo htmlspecialchars($message); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <?php if ($setup_complete): ?>
                    <div class="text-center">
                        <h3 style="color: var(--primary-green);">🎉 Setup Complete!</h3>
                        <p>Your Musanze Marketplace platform is ready to use.</p>
                        <a href="index.php" class="btn btn-primary btn-lg">
                            <i class="fas fa-home"></i> Go to Homepage
                        </a>
                        <div style="margin-top: 2rem; padding: 1rem; background: var(--light-green); border-radius: var(--border-radius);">
                            <h5>Demo Accounts:</h5>
                            <p><strong>Customer:</strong> <EMAIL> / demo123</p>
                            <p><strong>Vendor:</strong> <EMAIL> / demo123</p>
                            <p><strong>Admin:</strong> <EMAIL> / admin123</p>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="setup-steps">
                        <h4>Setup Steps</h4>
                        <p>Follow these steps to set up your Musanze Marketplace platform:</p>
                        
                        <div class="setup-step">
                            <h5>1. Test Database Connection</h5>
                            <p>Verify that PHP can connect to your MySQL database.</p>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="test_connection">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-database"></i> Test Connection
                                </button>
                            </form>
                        </div>
                        
                        <div class="setup-step">
                            <h5>2. Create Database</h5>
                            <p>Create the 'musanze_marketplace' database if it doesn't exist.</p>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="create_database">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Create Database
                                </button>
                            </form>
                        </div>
                        
                        <div class="setup-step">
                            <h5>3. Import Database Schema</h5>
                            <p>Import the database tables and structure.</p>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="import_schema">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-download"></i> Import Schema
                                </button>
                            </form>
                        </div>
                        
                        <div class="setup-step">
                            <h5>4. Create Demo Data</h5>
                            <p>Create demo users, vendors, and products for testing.</p>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="create_demo_data">
                                <button type="submit" class="btn btn-secondary">
                                    <i class="fas fa-users"></i> Create Demo Data
                                </button>
                            </form>
                        </div>
                        
                        <div class="setup-step">
                            <h5>5. Complete Setup</h5>
                            <p>Finalize the setup and create necessary directories.</p>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="complete_setup">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-check"></i> Complete Setup
                                </button>
                            </form>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> Requirements</h5>
            </div>
            <div class="card-body">
                <div class="requirements-check">
                    <div class="requirement">
                        <span class="<?php echo version_compare(PHP_VERSION, '8.0.0', '>=') ? 'text-success' : 'text-danger'; ?>">
                            <i class="fas <?php echo version_compare(PHP_VERSION, '8.0.0', '>=') ? 'fa-check' : 'fa-times'; ?>"></i>
                            PHP <?php echo PHP_VERSION; ?> (8.0+ required)
                        </span>
                    </div>
                    <div class="requirement">
                        <span class="<?php echo extension_loaded('pdo_mysql') ? 'text-success' : 'text-danger'; ?>">
                            <i class="fas <?php echo extension_loaded('pdo_mysql') ? 'fa-check' : 'fa-times'; ?>"></i>
                            PDO MySQL Extension
                        </span>
                    </div>
                    <div class="requirement">
                        <span class="<?php echo is_writable('.') ? 'text-success' : 'text-danger'; ?>">
                            <i class="fas <?php echo is_writable('.') ? 'fa-check' : 'fa-times'; ?>"></i>
                            Directory Write Permissions
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <style>
    .setup-step {
        margin-bottom: 2rem;
        padding: 1rem;
        border: 1px solid var(--gray-medium);
        border-radius: var(--border-radius);
    }
    
    .setup-step h5 {
        color: var(--primary-green);
        margin-bottom: 0.5rem;
    }
    
    .requirement {
        margin-bottom: 0.5rem;
    }
    
    .text-success {
        color: #4CAF50;
    }
    
    .text-danger {
        color: #f44336;
    }
    </style>
</body>
</html>
