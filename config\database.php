<?php
/**
 * Musanze District Marketplace - Database Configuration
 * Professional Product Price Comparison Platform
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'musanze_marketplace';
    private $username = 'root';
    private $password = '';
    private $conn;

    public function getConnection() {
        $this->conn = null;
        
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8"
                )
            );
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }
        
        return $this->conn;
    }
}

// Application Configuration
define('APP_NAME', 'Musanze Marketplace');
define('APP_VERSION', '1.0.0');
define('BASE_URL', 'http://localhost/ange Final/');
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5242880); // 5MB

// Email Configuration
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');

// Security Configuration
define('JWT_SECRET', 'your-secret-key-here');
define('PASSWORD_SALT', 'musanze-marketplace-salt');

// Pagination
define('PRODUCTS_PER_PAGE', 12);
define('ORDERS_PER_PAGE', 10);
?>
