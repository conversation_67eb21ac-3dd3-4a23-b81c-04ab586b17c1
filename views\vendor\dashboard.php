<?php
$current_page = 'vendor-dashboard';
$page_title = 'Vendor Dashboard';
$page_description = 'Manage your products, orders, and business analytics';

// Include vendor authentication
require_once '../includes/vendor-auth.php';

// Require verified vendor access
if (!$vendorAuth->requireVerifiedVendor()) {
    exit; // Redirect handled by requireVerifiedVendor
}

// Get current vendor data
$vendor = $vendorAuth->getCurrentUser();
$vendor_id = $vendorAuth->getVendorId();

include '../includes/header.php';
?>

<div class="container" style="padding: 2rem 0;">
    <!-- Dashboard Header -->
    <div class="dashboard-header mb-4">
        <div class="row align-center justify-between">
            <div class="col">
                <h2>
                    <i class="fas fa-store" style="color: var(--primary-green);"></i>
                    <?php echo htmlspecialchars($vendor['business_name'] ?: 'Vendor Dashboard'); ?>
                </h2>
                <p style="color: var(--gray-dark); margin: 0;">
                    Welcome back, <?php echo htmlspecialchars($vendor['first_name']); ?>!
                    Manage your business on Musanze Marketplace
                </p>
            </div>
            <div class="col text-right">
                <div class="vendor-status">
                    <span class="badge verified" style="background: var(--secondary-green); color: white; padding: 0.5rem 1rem; border-radius: var(--border-radius); margin-right: 0.5rem;">
                        <i class="fas fa-check-circle"></i> Verified Vendor
                    </span>
                    <div class="vendor-info" style="font-size: 0.9rem; color: var(--gray-dark); margin-top: 0.25rem;">
                        ID: #<?php echo $vendor_id; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col col-3">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-bars"></i> Vendor Menu</h5>
                </div>
                <div class="card-body" style="padding: 0;">
                    <nav class="dashboard-nav">
                        <a href="#overview" class="nav-item active" onclick="showSection('overview')">
                            <i class="fas fa-chart-line"></i> Overview
                        </a>
                        <a href="#products" class="nav-item" onclick="showSection('products')">
                            <i class="fas fa-box"></i> Products
                        </a>
                        <a href="#orders" class="nav-item" onclick="showSection('orders')">
                            <i class="fas fa-shopping-bag"></i> Orders
                        </a>
                        <a href="#analytics" class="nav-item" onclick="showSection('analytics')">
                            <i class="fas fa-chart-bar"></i> Analytics
                        </a>
                        <a href="#profile" class="nav-item" onclick="showSection('profile')">
                            <i class="fas fa-store-alt"></i> Business Profile
                        </a>
                        <a href="#settings" class="nav-item" onclick="showSection('settings')">
                            <i class="fas fa-cog"></i> Settings
                        </a>
                    </nav>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col col-9">
            <!-- Overview Section -->
            <div id="overview-section" class="dashboard-section">
                <!-- Quick Stats -->
                <div class="row mb-4">
                    <div class="col col-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="stat-content">
                                <h4 id="total-products">0</h4>
                                <p>Total Products</p>
                                <small id="active-products-info" style="color: var(--gray-dark);"></small>
                            </div>
                        </div>
                    </div>

                    <div class="col col-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-shopping-bag"></i>
                            </div>
                            <div class="stat-content">
                                <h4 id="total-orders">0</h4>
                                <p>Total Orders</p>
                                <small id="pending-orders-info" style="color: var(--gray-dark);"></small>
                            </div>
                        </div>
                    </div>

                    <div class="col col-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div class="stat-content">
                                <h4 id="total-revenue">RWF 0</h4>
                                <p>Total Revenue</p>
                                <small id="monthly-revenue-info" style="color: var(--gray-dark);"></small>
                            </div>
                        </div>
                    </div>

                    <div class="col col-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="stat-content">
                                <h4 id="average-rating">0.0</h4>
                                <p>Average Rating</p>
                                <small id="total-reviews-info" style="color: var(--gray-dark);"></small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Metrics -->
                <div class="row mb-4">
                    <div class="col col-12">
                        <div id="performance-metrics" class="performance-container">
                            <!-- Performance data will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Recent Orders -->
                <div class="card mb-4">
                    <div class="card-header">
                        <div class="row align-center justify-between">
                            <div class="col">
                                <h5><i class="fas fa-clock"></i> Recent Orders</h5>
                            </div>
                            <div class="col text-right">
                                <a href="#orders" onclick="showSection('orders')" class="btn btn-sm btn-secondary">
                                    View All Orders
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="recent-orders">
                            <div class="text-center" style="padding: 2rem;">
                                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: var(--primary-green);"></i>
                                <p style="margin-top: 1rem;">Loading recent orders...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="quick-actions">
                            <a href="#products" class="action-btn" onclick="showSection('products')">
                                <i class="fas fa-plus"></i>
                                <span>Add Product</span>
                            </a>
                            <a href="#orders" class="action-btn" onclick="showSection('orders')">
                                <i class="fas fa-list"></i>
                                <span>Manage Orders</span>
                            </a>
                            <a href="#analytics" class="action-btn" onclick="showSection('analytics')">
                                <i class="fas fa-chart-bar"></i>
                                <span>View Analytics</span>
                            </a>
                            <a href="#profile" class="action-btn" onclick="showSection('profile')">
                                <i class="fas fa-edit"></i>
                                <span>Edit Profile</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products Section -->
            <div id="products-section" class="dashboard-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <div class="row align-center justify-between">
                            <div class="col">
                                <h5><i class="fas fa-box"></i> My Products</h5>
                            </div>
                            <div class="col text-right">
                                <button class="btn btn-primary" onclick="showAddProductForm()">
                                    <i class="fas fa-plus"></i> Add New Product
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="products-content">
                            <div class="text-center" style="padding: 2rem;">
                                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: var(--primary-green);"></i>
                                <p style="margin-top: 1rem;">Loading products...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Orders Section -->
            <div id="orders-section" class="dashboard-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-shopping-bag"></i> Order Management</h5>
                    </div>
                    <div class="card-body">
                        <!-- Order Filters -->
                        <div class="order-filters mb-3">
                            <div class="row">
                                <div class="col col-3">
                                    <select class="form-control form-select" id="order-status-filter">
                                        <option value="">All Orders</option>
                                        <option value="pending">Pending</option>
                                        <option value="confirmed">Confirmed</option>
                                        <option value="processing">Processing</option>
                                        <option value="shipped">Shipped</option>
                                        <option value="delivered">Delivered</option>
                                        <option value="cancelled">Cancelled</option>
                                    </select>
                                </div>
                                <div class="col col-3">
                                    <input type="date" class="form-control" id="order-date-filter">
                                </div>
                                <div class="col col-3">
                                    <input type="text" class="form-control" placeholder="Search orders..." id="order-search">
                                </div>
                                <div class="col col-3">
                                    <button class="btn btn-secondary w-100" onclick="filterOrders()">
                                        <i class="fas fa-filter"></i> Filter
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div id="orders-content">
                            <div class="text-center" style="padding: 2rem;">
                                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: var(--primary-green);"></i>
                                <p style="margin-top: 1rem;">Loading orders...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analytics Section -->
            <div id="analytics-section" class="dashboard-section" style="display: none;">
                <div class="row">
                    <div class="col col-12">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-line"></i> Sales Analytics</h5>
                            </div>
                            <div class="card-body">
                                <div class="analytics-placeholder text-center" style="padding: 3rem;">
                                    <i class="fas fa-chart-bar" style="font-size: 4rem; color: var(--gray-medium);"></i>
                                    <h5>Analytics Dashboard</h5>
                                    <p style="color: var(--gray-dark);">Detailed analytics and reporting features coming soon!</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col col-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-trophy"></i> Top Products</h6>
                            </div>
                            <div class="card-body">
                                <div id="top-products">
                                    <div class="text-center" style="padding: 2rem;">
                                        <p style="color: var(--gray-dark);">No sales data available yet</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col col-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-calendar"></i> Recent Activity</h6>
                            </div>
                            <div class="card-body">
                                <div id="recent-activity">
                                    <div class="text-center" style="padding: 2rem;">
                                        <p style="color: var(--gray-dark);">No recent activity</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Business Profile Section -->
            <div id="profile-section" class="dashboard-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-store-alt"></i> Business Profile</h5>
                    </div>
                    <div class="card-body">
                        <form id="business-profile-form">
                            <div class="form-group">
                                <label class="form-label">Business Name</label>
                                <input type="text" name="business_name" class="form-control" 
                                       placeholder="Enter your business name">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Business Description</label>
                                <textarea name="business_description" class="form-control" rows="4" 
                                          placeholder="Describe your business and what you sell"></textarea>
                            </div>
                            
                            <div class="row">
                                <div class="col col-6">
                                    <div class="form-group">
                                        <label class="form-label">Business Address</label>
                                        <input type="text" name="business_address" class="form-control" 
                                               placeholder="Enter your business address">
                                    </div>
                                </div>
                                <div class="col col-6">
                                    <div class="form-group">
                                        <label class="form-label">Business Phone</label>
                                        <input type="tel" name="business_phone" class="form-control" 
                                               placeholder="+250 788 123 456">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col col-6">
                                    <div class="form-group">
                                        <label class="form-label">Business Email</label>
                                        <input type="email" name="business_email" class="form-control" 
                                               placeholder="<EMAIL>">
                                    </div>
                                </div>
                                <div class="col col-6">
                                    <div class="form-group">
                                        <label class="form-label">Tax Number (TIN)</label>
                                        <input type="text" name="tax_number" class="form-control" 
                                               placeholder="Enter your TIN number">
                                    </div>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Business Profile
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Settings Section -->
            <div id="settings-section" class="dashboard-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cog"></i> Account Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="settings-grid">
                            <div class="setting-item">
                                <h6>Notification Preferences</h6>
                                <p style="color: var(--gray-dark); margin-bottom: 1rem;">Manage how you receive notifications</p>
                                <div class="form-group">
                                    <label style="display: flex; align-items: center; margin: 0;">
                                        <input type="checkbox" style="margin-right: 0.5rem;" checked>
                                        Email notifications for new orders
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label style="display: flex; align-items: center; margin: 0;">
                                        <input type="checkbox" style="margin-right: 0.5rem;" checked>
                                        SMS notifications for urgent updates
                                    </label>
                                </div>
                            </div>
                            
                            <div class="setting-item">
                                <h6>Store Settings</h6>
                                <p style="color: var(--gray-dark); margin-bottom: 1rem;">Configure your store preferences</p>
                                <div class="form-group">
                                    <label class="form-label">Store Status</label>
                                    <select class="form-control form-select">
                                        <option value="active">Active - Accepting Orders</option>
                                        <option value="vacation">Vacation Mode</option>
                                        <option value="maintenance">Under Maintenance</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <button class="btn btn-primary mt-3">
                            <i class="fas fa-save"></i> Save Settings
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.dashboard-nav {
    display: flex;
    flex-direction: column;
}

.dashboard-nav .nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    color: var(--gray-dark);
    text-decoration: none;
    border-bottom: 1px solid var(--gray-light);
    transition: var(--transition);
}

.dashboard-nav .nav-item:hover,
.dashboard-nav .nav-item.active {
    background: var(--light-green);
    color: var(--primary-green);
}

.dashboard-nav .nav-item:last-child {
    border-bottom: none;
}

.stat-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.stat-icon {
    background: var(--light-green);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-green);
    font-size: 1.5rem;
}

.stat-content h4 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--black);
}

.stat-content p {
    margin: 0;
    color: var(--gray-dark);
    font-size: 0.9rem;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1.5rem;
    background: var(--gray-light);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--gray-dark);
    transition: var(--transition);
}

.action-btn:hover {
    background: var(--light-green);
    color: var(--primary-green);
    transform: translateY(-2px);
}

.action-btn i {
    font-size: 1.5rem;
}

.settings-grid {
    display: grid;
    gap: 2rem;
}

.setting-item {
    padding: 1.5rem;
    border: 1px solid var(--gray-medium);
    border-radius: var(--border-radius);
}

.setting-item h6 {
    color: var(--primary-green);
    margin-bottom: 0.5rem;
}

.performance-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.performance-card {
    background: var(--white);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
    border-left: 4px solid var(--primary-green);
}

.performance-card h6 {
    color: var(--gray-dark);
    margin-bottom: 1rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.alert {
    padding: 1rem;
    border-radius: var(--border-radius);
    border: 1px solid transparent;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.alert-warning {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-info {
    background: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.order-item {
    transition: var(--transition);
}

.order-item:hover {
    background: var(--light-green);
    border-color: var(--primary-green) !important;
}

@media (max-width: 768px) {
    .row {
        flex-direction: column;
    }
    
    .col {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .dashboard-nav {
        flex-direction: row;
        overflow-x: auto;
    }
    
    .dashboard-nav .nav-item {
        white-space: nowrap;
        border-bottom: none;
        border-right: 1px solid var(--gray-light);
    }
    
    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    loadVendorDashboardData();
});

function showSection(sectionName) {
    // Hide all sections
    const sections = document.querySelectorAll('.dashboard-section');
    sections.forEach(section => section.style.display = 'none');
    
    // Show selected section
    document.getElementById(sectionName + '-section').style.display = 'block';
    
    // Update navigation
    const navItems = document.querySelectorAll('.dashboard-nav .nav-item');
    navItems.forEach(item => item.classList.remove('active'));
    event.target.classList.add('active');
    
    // Load section-specific data
    switch(sectionName) {
        case 'products':
            loadProducts();
            break;
        case 'orders':
            loadOrders();
            break;
        case 'analytics':
            loadAnalytics();
            break;
    }
}

function loadVendorDashboardData() {
    // Load comprehensive dashboard data
    loadVendorOverview();
}

function loadVendorOverview() {
    fetch('<?php echo BASE_URL; ?>api/vendor-dashboard.php?action=overview')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateDashboardStats(data.overview.stats);
                updateRecentOrders(data.overview.recent_orders);
                updateNotifications(data.overview.notifications);
                updatePerformanceMetrics(data.overview.performance);
            } else {
                console.error('Failed to load dashboard data:', data.message);
                showNotification('Failed to load dashboard data', 'error');
            }
        })
        .catch(error => {
            console.error('Dashboard data error:', error);
            showNotification('Error loading dashboard data', 'error');
        });
}

function updateDashboardStats(stats) {
    // Update main stat cards with real data
    document.getElementById('total-products').textContent = stats.total_products;
    document.getElementById('total-orders').textContent = stats.total_orders;
    document.getElementById('total-revenue').textContent = 'RWF ' + formatPrice(stats.total_revenue);
    document.getElementById('average-rating').textContent = stats.average_rating.toFixed(1);

    // Update additional info fields
    const activeProductsInfo = document.getElementById('active-products-info');
    if (activeProductsInfo) {
        activeProductsInfo.textContent = `${stats.active_products} active`;
    }

    const pendingOrdersInfo = document.getElementById('pending-orders-info');
    if (pendingOrdersInfo) {
        if (stats.pending_orders > 0) {
            pendingOrdersInfo.innerHTML = `<span style="color: #ff9800;">${stats.pending_orders} pending</span>`;
        } else {
            pendingOrdersInfo.textContent = 'All up to date';
        }
    }

    const monthlyRevenueInfo = document.getElementById('monthly-revenue-info');
    if (monthlyRevenueInfo) {
        monthlyRevenueInfo.textContent = `RWF ${formatPrice(stats.monthly_revenue)} this month`;
    }

    const totalReviewsInfo = document.getElementById('total-reviews-info');
    if (totalReviewsInfo) {
        totalReviewsInfo.textContent = `${stats.total_reviews} review${stats.total_reviews !== 1 ? 's' : ''}`;
    }
}

function updateRecentOrders(orders) {
    const container = document.getElementById('recent-orders');

    if (orders.length === 0) {
        container.innerHTML = `
            <div class="text-center" style="padding: 2rem;">
                <i class="fas fa-shopping-bag" style="font-size: 3rem; color: var(--gray-medium);"></i>
                <h5>No orders yet</h5>
                <p style="color: var(--gray-dark);">Orders from customers will appear here</p>
            </div>
        `;
        return;
    }

    let html = '';
    orders.forEach(order => {
        const statusColor = getOrderStatusColor(order.status);
        html += `
            <div class="order-item" style="display: flex; justify-content: space-between; align-items: center; padding: 1rem; border: 1px solid var(--gray-medium); border-radius: var(--border-radius); margin-bottom: 1rem;">
                <div>
                    <h6 style="margin: 0;">Order #${order.order_number}</h6>
                    <p style="margin: 0; color: var(--gray-dark); font-size: 0.9rem;">
                        Customer: ${order.customer_name} • ${order.item_count} item(s) • RWF ${formatPrice(order.total_amount)}
                    </p>
                    <small style="color: var(--gray-dark);">${formatDate(order.created_at)}</small>
                </div>
                <div>
                    <span class="badge" style="background: ${statusColor}; color: white; padding: 0.25rem 0.5rem; border-radius: var(--border-radius); font-size: 0.8rem;">
                        ${capitalizeFirst(order.status)}
                    </span>
                </div>
            </div>
        `;
    });

    html += `
        <div class="text-center">
            <a href="#orders" onclick="showSection('orders')" class="btn btn-secondary">
                <i class="fas fa-list"></i> View All Orders
            </a>
        </div>
    `;

    container.innerHTML = html;
}

function updateNotifications(notifications) {
    if (notifications.length === 0) return;

    // Create notifications container if it doesn't exist
    let notificationContainer = document.getElementById('dashboard-notifications');
    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.id = 'dashboard-notifications';
        notificationContainer.style.cssText = 'margin-bottom: 2rem;';

        const dashboardHeader = document.querySelector('.dashboard-header');
        dashboardHeader.parentNode.insertBefore(notificationContainer, dashboardHeader.nextSibling);
    }

    let html = '';
    notifications.forEach(notification => {
        const alertClass = notification.type === 'warning' ? 'alert-warning' : 'alert-info';
        html += `
            <div class="alert ${alertClass}" style="margin-bottom: 1rem;">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <strong>${notification.title}</strong>
                        <p style="margin: 0.25rem 0 0 0;">${notification.message}</p>
                    </div>
                    <a href="${notification.action_url}" class="btn btn-sm btn-primary">
                        View
                    </a>
                </div>
            </div>
        `;
    });

    notificationContainer.innerHTML = html;
}

function updatePerformanceMetrics(performance) {
    // Add performance indicators to the dashboard
    const performanceContainer = document.getElementById('performance-metrics');
    if (performanceContainer) {
        const growthColor = performance.sales_growth >= 0 ? 'var(--secondary-green)' : '#f44336';
        const growthIcon = performance.sales_growth >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';

        performanceContainer.innerHTML = `
            <div class="performance-card">
                <h6>Sales Growth</h6>
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <span style="font-size: 1.5rem; font-weight: 700; color: ${growthColor};">
                        ${performance.sales_growth}%
                    </span>
                    <i class="fas ${growthIcon}" style="color: ${growthColor};"></i>
                </div>
                <small style="color: var(--gray-dark);">vs last month</small>
            </div>
        `;
    }
}

function loadProducts() {
    document.getElementById('products-content').innerHTML = `
        <div class="text-center" style="padding: 3rem;">
            <i class="fas fa-box" style="font-size: 3rem; color: var(--gray-medium);"></i>
            <h5>No products yet</h5>
            <p style="color: var(--gray-dark);">Start by adding your first product</p>
            <button class="btn btn-primary" onclick="showAddProductForm()">
                <i class="fas fa-plus"></i> Add Your First Product
            </button>
        </div>
    `;
}

function loadOrders() {
    document.getElementById('orders-content').innerHTML = `
        <div class="text-center" style="padding: 3rem;">
            <i class="fas fa-shopping-bag" style="font-size: 3rem; color: var(--gray-medium);"></i>
            <h5>No orders yet</h5>
            <p style="color: var(--gray-dark);">Orders from customers will appear here</p>
        </div>
    `;
}

function loadAnalytics() {
    // Analytics data would be loaded here
    console.log('Loading analytics data...');
}

function showAddProductForm() {
    showNotification('Add product feature coming soon!', 'info');
}

function filterOrders() {
    showNotification('Order filtering feature coming soon!', 'info');
}

// Helper functions
function formatPrice(price) {
    return new Intl.NumberFormat('en-RW').format(price);
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function capitalizeFirst(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
}

function getOrderStatusColor(status) {
    const colors = {
        'pending': '#ff9800',
        'confirmed': '#2196F3',
        'processing': '#9C27B0',
        'shipped': '#607D8B',
        'delivered': '#4CAF50',
        'cancelled': '#f44336',
        'refunded': '#795548'
    };
    return colors[status] || '#757575';
}

// Auto-refresh dashboard data every 5 minutes
setInterval(() => {
    loadVendorOverview();
}, 300000);

// Show notification function
function showNotification(message, type = 'info', duration = 5000) {
    let container = document.getElementById('notification-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notification-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
        `;
        document.body.appendChild(container);
    }
    
    const notification = document.createElement('div');
    
    const colors = {
        success: '#4CAF50',
        error: '#f44336',
        warning: '#ff9800',
        info: '#2196F3'
    };
    
    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };
    
    notification.style.cssText = `
        background: ${colors[type]};
        color: white;
        padding: 15px 20px;
        margin-bottom: 10px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        display: flex;
        align-items: center;
        gap: 10px;
        animation: slideIn 0.3s ease;
        cursor: pointer;
    `;
    
    notification.innerHTML = `
        <i class="${icons[type]}"></i>
        <span>${message}</span>
        <i class="fas fa-times" style="margin-left: auto; cursor: pointer;"></i>
    `;
    
    notification.addEventListener('click', () => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    });
    
    container.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }
    }, duration);
}
</script>

<?php include '../includes/footer.php'; ?>
