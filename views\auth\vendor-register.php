<?php
$current_page = 'vendor-register';
$page_title = 'Vendor Registration';
$page_description = 'Join Musanze Marketplace as a vendor and grow your business';

// Redirect if already logged in
session_start();
if (isset($_SESSION['user_id'])) {
    $redirect_url = $_SESSION['user_type'] === 'vendor' ? 'vendor-dashboard' : 
                   ($_SESSION['user_type'] === 'admin' ? 'admin' : 'dashboard');
    header('Location: ' . BASE_URL . $redirect_url);
    exit;
}

include '../includes/header.php';
?>

<div class="container" style="padding: 3rem 0;">
    <div class="row justify-center">
        <div class="col col-10" style="max-width: 800px;">
            <div class="card">
                <div class="card-header text-center">
                    <h3><i class="fas fa-store" style="color: var(--primary-green);"></i> Become a Vendor</h3>
                    <p style="color: var(--gray-dark); margin: 0;">Join our marketplace and reach thousands of customers in Musanze District</p>
                </div>
                
                <div class="card-body">
                    <div class="vendor-benefits mb-4" style="background: var(--light-green); padding: 1.5rem; border-radius: var(--border-radius);">
                        <h5 style="color: var(--primary-green); margin-bottom: 1rem;">
                            <i class="fas fa-star"></i> Why Sell on Musanze Marketplace?
                        </h5>
                        <div class="row">
                            <div class="col col-6">
                                <ul style="list-style: none; padding: 0;">
                                    <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: var(--secondary-green); margin-right: 0.5rem;"></i> Reach thousands of local customers</li>
                                    <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: var(--secondary-green); margin-right: 0.5rem;"></i> Easy product management tools</li>
                                    <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: var(--secondary-green); margin-right: 0.5rem;"></i> Real-time order notifications</li>
                                </ul>
                            </div>
                            <div class="col col-6">
                                <ul style="list-style: none; padding: 0;">
                                    <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: var(--secondary-green); margin-right: 0.5rem;"></i> Competitive commission rates</li>
                                    <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: var(--secondary-green); margin-right: 0.5rem;"></i> Marketing and promotional support</li>
                                    <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: var(--secondary-green); margin-right: 0.5rem;"></i> Dedicated vendor support</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <form id="vendor-register-form">
                        <h5 style="color: var(--primary-green); margin-bottom: 1rem;">Personal Information</h5>
                        
                        <div class="row">
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-user"></i> First Name *
                                    </label>
                                    <input type="text" 
                                           name="first_name" 
                                           class="form-control" 
                                           placeholder="Enter your first name"
                                           required>
                                </div>
                            </div>
                            
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-user"></i> Last Name *
                                    </label>
                                    <input type="text" 
                                           name="last_name" 
                                           class="form-control" 
                                           placeholder="Enter your last name"
                                           required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-at"></i> Username *
                                    </label>
                                    <input type="text" 
                                           name="username" 
                                           class="form-control" 
                                           placeholder="Choose a username"
                                           required>
                                </div>
                            </div>
                            
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-envelope"></i> Email Address *
                                    </label>
                                    <input type="email" 
                                           name="email" 
                                           class="form-control" 
                                           placeholder="Enter your email"
                                           required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-phone"></i> Phone Number *
                                    </label>
                                    <input type="tel" 
                                           name="phone" 
                                           class="form-control" 
                                           placeholder="+250 788 123 456"
                                           required>
                                </div>
                            </div>
                            
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-map-marker-alt"></i> Personal Address
                                    </label>
                                    <input type="text" 
                                           name="address" 
                                           class="form-control" 
                                           placeholder="Enter your address">
                                </div>
                            </div>
                        </div>
                        
                        <hr style="margin: 2rem 0;">
                        
                        <h5 style="color: var(--primary-green); margin-bottom: 1rem;">Business Information</h5>
                        
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-store"></i> Business Name *
                            </label>
                            <input type="text" 
                                   name="business_name" 
                                   class="form-control" 
                                   placeholder="Enter your business name"
                                   required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-align-left"></i> Business Description
                            </label>
                            <textarea name="business_description" 
                                      class="form-control" 
                                      rows="3" 
                                      placeholder="Describe your business and what you sell"></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-map-marker-alt"></i> Business Address *
                                    </label>
                                    <input type="text" 
                                           name="business_address" 
                                           class="form-control" 
                                           placeholder="Enter your business address"
                                           required>
                                </div>
                            </div>
                            
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-phone"></i> Business Phone
                                    </label>
                                    <input type="tel" 
                                           name="business_phone" 
                                           class="form-control" 
                                           placeholder="+250 788 123 456">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-envelope"></i> Business Email
                                    </label>
                                    <input type="email" 
                                           name="business_email" 
                                           class="form-control" 
                                           placeholder="<EMAIL>">
                                </div>
                            </div>
                            
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-file-alt"></i> Tax Number (TIN)
                                    </label>
                                    <input type="text" 
                                           name="tax_number" 
                                           class="form-control" 
                                           placeholder="Enter your TIN number">
                                </div>
                            </div>
                        </div>
                        
                        <hr style="margin: 2rem 0;">
                        
                        <h5 style="color: var(--primary-green); margin-bottom: 1rem;">Account Security</h5>
                        
                        <div class="row">
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-lock"></i> Password *
                                    </label>
                                    <div style="position: relative;">
                                        <input type="password" 
                                               name="password" 
                                               class="form-control" 
                                               placeholder="Create a password"
                                               id="password-input"
                                               required>
                                        <button type="button" 
                                                class="password-toggle"
                                                onclick="togglePassword('password-input', 'password-toggle-icon')"
                                                style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); background: none; border: none; color: var(--gray-dark); cursor: pointer;">
                                            <i class="fas fa-eye" id="password-toggle-icon"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-lock"></i> Confirm Password *
                                    </label>
                                    <div style="position: relative;">
                                        <input type="password" 
                                               name="confirm_password" 
                                               class="form-control" 
                                               placeholder="Confirm your password"
                                               id="confirm-password-input"
                                               required>
                                        <button type="button" 
                                                class="password-toggle"
                                                onclick="togglePassword('confirm-password-input', 'confirm-password-toggle-icon')"
                                                style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); background: none; border: none; color: var(--gray-dark); cursor: pointer;">
                                            <i class="fas fa-eye" id="confirm-password-toggle-icon"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label style="display: flex; align-items: flex-start; margin: 0;">
                                <input type="checkbox" name="terms" style="margin-right: 0.5rem; margin-top: 0.2rem;" required>
                                <span style="font-size: 0.9rem;">
                                    I agree to the <a href="<?php echo BASE_URL; ?>terms" style="color: var(--primary-green);">Terms of Service</a>, 
                                    <a href="<?php echo BASE_URL; ?>privacy" style="color: var(--primary-green);">Privacy Policy</a>, and 
                                    <a href="<?php echo BASE_URL; ?>vendor-terms" style="color: var(--primary-green);">Vendor Agreement</a>
                                </span>
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 btn-lg" id="vendor-register-btn">
                            <i class="fas fa-store"></i> Submit Vendor Application
                        </button>
                    </form>
                    
                    <div class="text-center mt-3">
                        <p style="color: var(--gray-dark);">
                            Already have an account? 
                            <a href="<?php echo BASE_URL; ?>login" style="color: var(--primary-green); font-weight: 500;">
                                Login here
                            </a>
                        </p>
                        <p style="color: var(--gray-dark);">
                            Just want to shop? 
                            <a href="<?php echo BASE_URL; ?>register" style="color: var(--secondary-green); font-weight: 500;">
                                Create Customer Account
                            </a>
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Application Process -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle"></i> Application Process</h5>
                </div>
                <div class="card-body">
                    <div class="process-steps" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <div class="process-step text-center">
                            <div style="background: var(--light-green); width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem;">
                                <i class="fas fa-file-alt" style="font-size: 1.5rem; color: var(--primary-green);"></i>
                            </div>
                            <h6>1. Submit Application</h6>
                            <p style="font-size: 0.9rem; color: var(--gray-dark);">Fill out the vendor registration form</p>
                        </div>
                        
                        <div class="process-step text-center">
                            <div style="background: var(--light-green); width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem;">
                                <i class="fas fa-search" style="font-size: 1.5rem; color: var(--primary-green);"></i>
                            </div>
                            <h6>2. Review Process</h6>
                            <p style="font-size: 0.9rem; color: var(--gray-dark);">We review your application within 24-48 hours</p>
                        </div>
                        
                        <div class="process-step text-center">
                            <div style="background: var(--light-green); width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem;">
                                <i class="fas fa-check-circle" style="font-size: 1.5rem; color: var(--primary-green);"></i>
                            </div>
                            <h6>3. Approval</h6>
                            <p style="font-size: 0.9rem; color: var(--gray-dark);">Get approved and start selling immediately</p>
                        </div>
                        
                        <div class="process-step text-center">
                            <div style="background: var(--light-green); width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem;">
                                <i class="fas fa-rocket" style="font-size: 1.5rem; color: var(--primary-green);"></i>
                            </div>
                            <h6>4. Start Selling</h6>
                            <p style="font-size: 0.9rem; color: var(--gray-dark);">List your products and grow your business</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const vendorRegisterForm = document.getElementById('vendor-register-form');
    const vendorRegisterBtn = document.getElementById('vendor-register-btn');
    
    vendorRegisterForm.addEventListener('submit', function(e) {
        e.preventDefault();
        handleVendorRegister();
    });
    
    // Real-time password validation
    const passwordInput = document.querySelector('input[name="password"]');
    const confirmPasswordInput = document.querySelector('input[name="confirm_password"]');
    
    confirmPasswordInput.addEventListener('input', function() {
        if (this.value && passwordInput.value !== this.value) {
            this.setCustomValidity('Passwords do not match');
        } else {
            this.setCustomValidity('');
        }
    });
});

function handleVendorRegister() {
    const form = document.getElementById('vendor-register-form');
    const formData = new FormData(form);
    const vendorRegisterBtn = document.getElementById('vendor-register-btn');
    
    // Validate passwords match
    if (formData.get('password') !== formData.get('confirm_password')) {
        showNotification('Passwords do not match', 'error');
        return;
    }
    
    // Disable button and show loading
    vendorRegisterBtn.disabled = true;
    vendorRegisterBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Submitting Application...';
    
    const data = {
        action: 'vendor_register',
        username: formData.get('username'),
        email: formData.get('email'),
        password: formData.get('password'),
        confirm_password: formData.get('confirm_password'),
        first_name: formData.get('first_name'),
        last_name: formData.get('last_name'),
        phone: formData.get('phone'),
        address: formData.get('address'),
        business_name: formData.get('business_name'),
        business_description: formData.get('business_description'),
        business_address: formData.get('business_address'),
        business_phone: formData.get('business_phone'),
        business_email: formData.get('business_email'),
        tax_number: formData.get('tax_number')
    };
    
    fetch('<?php echo BASE_URL; ?>api/auth.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            
            // Redirect after short delay
            setTimeout(() => {
                window.location.href = data.redirect || '<?php echo BASE_URL; ?>login';
            }, 2000);
        } else {
            showNotification(data.message, 'error');
            
            // Re-enable button
            vendorRegisterBtn.disabled = false;
            vendorRegisterBtn.innerHTML = '<i class="fas fa-store"></i> Submit Vendor Application';
        }
    })
    .catch(error => {
        console.error('Vendor registration error:', error);
        showNotification('An error occurred. Please try again.', 'error');
        
        // Re-enable button
        vendorRegisterBtn.disabled = false;
        vendorRegisterBtn.innerHTML = '<i class="fas fa-store"></i> Submit Vendor Application';
    });
}

function togglePassword(inputId, iconId) {
    const passwordInput = document.getElementById(inputId);
    const toggleIcon = document.getElementById(iconId);
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleIcon.className = 'fas fa-eye';
    }
}

// Show notification function
function showNotification(message, type = 'info', duration = 5000) {
    let container = document.getElementById('notification-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notification-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
        `;
        document.body.appendChild(container);
    }
    
    const notification = document.createElement('div');
    
    const colors = {
        success: '#4CAF50',
        error: '#f44336',
        warning: '#ff9800',
        info: '#2196F3'
    };
    
    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };
    
    notification.style.cssText = `
        background: ${colors[type]};
        color: white;
        padding: 15px 20px;
        margin-bottom: 10px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        display: flex;
        align-items: center;
        gap: 10px;
        animation: slideIn 0.3s ease;
        cursor: pointer;
    `;
    
    notification.innerHTML = `
        <i class="${icons[type]}"></i>
        <span>${message}</span>
        <i class="fas fa-times" style="margin-left: auto; cursor: pointer;"></i>
    `;
    
    notification.addEventListener('click', () => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    });
    
    container.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }
    }, duration);
}
</script>

<style>
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 2rem 0;
    }
    
    .col {
        max-width: none !important;
    }
    
    .row .col {
        flex: 0 0 100%;
    }
    
    .process-steps {
        grid-template-columns: 1fr;
    }
}
</style>

<?php include '../includes/footer.php'; ?>
