<?php
$current_page = 'cart';
$page_title = 'Shopping Cart';
$page_description = 'Review your selected items and proceed to checkout';

include 'includes/header.php';
?>

<div class="container" style="padding: 2rem 0;">
    <!-- Breadcrumb -->
    <nav class="breadcrumb" style="margin-bottom: 2rem;">
        <a href="<?php echo BASE_URL; ?>">Home</a>
        <span style="margin: 0 0.5rem; color: var(--gray-dark);">/</span>
        <span>Shopping Cart</span>
    </nav>

    <div class="row">
        <!-- Cart Items -->
        <div class="col col-8">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-shopping-cart"></i> Your Shopping Cart</h4>
                </div>
                <div class="card-body">
                    <div id="cart-items-container">
                        <!-- Loading indicator -->
                        <div class="loading-indicator text-center" style="padding: 3rem;">
                            <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: var(--primary-green);"></i>
                            <p style="margin-top: 1rem;">Loading your cart...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cart Summary -->
        <div class="col col-4">
            <div class="card cart-summary">
                <div class="card-header">
                    <h5><i class="fas fa-calculator"></i> Order Summary</h5>
                </div>
                <div class="card-body">
                    <div id="cart-summary-content">
                        <div class="summary-row">
                            <span>Subtotal:</span>
                            <span id="cart-subtotal">RWF 0</span>
                        </div>
                        <div class="summary-row">
                            <span>Shipping:</span>
                            <span id="cart-shipping">RWF 2,000</span>
                        </div>
                        <div class="summary-row">
                            <span>Tax (18%):</span>
                            <span id="cart-tax">RWF 0</span>
                        </div>
                        <hr>
                        <div class="summary-row total">
                            <span><strong>Total:</strong></span>
                            <span id="cart-total"><strong>RWF 0</strong></span>
                        </div>
                    </div>
                    
                    <div class="cart-actions mt-3">
                        <button class="btn btn-primary w-100 btn-lg" id="checkout-btn" disabled>
                            <i class="fas fa-credit-card"></i> Proceed to Checkout
                        </button>
                        <a href="<?php echo BASE_URL; ?>products" class="btn btn-secondary w-100 mt-2">
                            <i class="fas fa-arrow-left"></i> Continue Shopping
                        </a>
                    </div>
                </div>
            </div>

            <!-- Promo Code -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6><i class="fas fa-tag"></i> Promo Code</h6>
                </div>
                <div class="card-body">
                    <div class="promo-code-form">
                        <div style="display: flex; gap: 0.5rem;">
                            <input type="text" 
                                   class="form-control" 
                                   placeholder="Enter promo code"
                                   id="promo-code-input">
                            <button class="btn btn-secondary" onclick="applyPromoCode()">
                                Apply
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recommended Products -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6><i class="fas fa-thumbs-up"></i> You Might Also Like</h6>
                </div>
                <div class="card-body">
                    <div id="recommended-products">
                        <div class="text-center" style="padding: 1rem;">
                            <p style="color: var(--gray-dark); font-size: 0.9rem;">Loading recommendations...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.cart-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-light);
    transition: var(--transition);
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item:hover {
    background: var(--gray-light);
}

.cart-item-image {
    width: 80px;
    height: 80px;
    border-radius: var(--border-radius);
    overflow: hidden;
    flex-shrink: 0;
}

.cart-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cart-item-details {
    flex: 1;
}

.cart-item-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--black);
}

.cart-item-vendor {
    color: var(--gray-dark);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.cart-item-price {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-green);
}

.cart-item-actions {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border: 1px solid var(--gray-medium);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.quantity-btn {
    background: var(--gray-light);
    border: none;
    padding: 0.5rem;
    cursor: pointer;
    transition: var(--transition);
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-btn:hover {
    background: var(--primary-green);
    color: var(--white);
}

.quantity-input {
    border: none;
    text-align: center;
    width: 50px;
    padding: 0.5rem;
    font-weight: 600;
}

.remove-btn {
    background: none;
    border: none;
    color: #f44336;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.remove-btn:hover {
    background: #ffebee;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.summary-row.total {
    font-size: 1.1rem;
    margin-bottom: 0;
}

.empty-cart {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-cart i {
    font-size: 4rem;
    color: var(--gray-medium);
    margin-bottom: 1rem;
}

.recommended-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border: 1px solid var(--gray-light);
    border-radius: var(--border-radius);
    margin-bottom: 0.75rem;
    transition: var(--transition);
}

.recommended-item:hover {
    border-color: var(--primary-green);
}

.recommended-item img {
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius);
    object-fit: cover;
}

.recommended-item-details {
    flex: 1;
}

.recommended-item-title {
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.recommended-item-price {
    font-size: 0.9rem;
    color: var(--primary-green);
    font-weight: 600;
}

@media (max-width: 768px) {
    .row {
        flex-direction: column;
    }
    
    .col {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .cart-item {
        flex-direction: column;
        text-align: center;
    }
    
    .cart-item-image {
        width: 120px;
        height: 120px;
    }
    
    .cart-item-actions {
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    loadCartItems();
    loadRecommendedProducts();
});

function loadCartItems() {
    fetch('<?php echo BASE_URL; ?>api/cart.php?action=list')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayCartItems(data.items, data.summary);
            } else {
                showEmptyCart();
            }
        })
        .catch(error => {
            console.error('Error loading cart:', error);
            showEmptyCart();
        });
}

function displayCartItems(items, summary) {
    const container = document.getElementById('cart-items-container');
    
    if (items.length === 0) {
        showEmptyCart();
        return;
    }
    
    let html = '';
    
    items.forEach(item => {
        html += `
            <div class="cart-item" data-product-id="${item.product_id}">
                <div class="cart-item-image">
                    <img src="${item.product_image || '<?php echo BASE_URL; ?>assets/images/placeholder.jpg'}" 
                         alt="${item.product_name}">
                </div>
                
                <div class="cart-item-details">
                    <div class="cart-item-title">${item.product_name}</div>
                    <div class="cart-item-vendor">Sold by: ${item.vendor_name}</div>
                    <div class="cart-item-price">RWF ${formatPrice(item.product_price)}</div>
                    ${item.stock_quantity < 5 ? `<div style="color: #f44336; font-size: 0.8rem;">Only ${item.stock_quantity} left in stock</div>` : ''}
                </div>
                
                <div class="cart-item-actions">
                    <div class="quantity-controls">
                        <button class="quantity-btn" onclick="updateQuantity(${item.product_id}, ${item.quantity - 1})">
                            <i class="fas fa-minus"></i>
                        </button>
                        <input type="number" 
                               class="quantity-input" 
                               value="${item.quantity}" 
                               min="1" 
                               max="${item.stock_quantity}"
                               onchange="updateQuantity(${item.product_id}, this.value)">
                        <button class="quantity-btn" onclick="updateQuantity(${item.product_id}, ${item.quantity + 1})">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    
                    <button class="remove-btn" onclick="removeFromCart(${item.product_id})" title="Remove item">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
    updateCartSummary(summary);
}

function showEmptyCart() {
    const container = document.getElementById('cart-items-container');
    container.innerHTML = `
        <div class="empty-cart">
            <i class="fas fa-shopping-cart"></i>
            <h4>Your cart is empty</h4>
            <p style="color: var(--gray-dark); margin-bottom: 2rem;">
                Looks like you haven't added any items to your cart yet.
            </p>
            <a href="<?php echo BASE_URL; ?>products" class="btn btn-primary">
                <i class="fas fa-search"></i> Start Shopping
            </a>
        </div>
    `;
    
    // Disable checkout button
    document.getElementById('checkout-btn').disabled = true;
    
    // Update summary
    updateCartSummary({
        total_items: 0,
        total_amount: 0,
        currency: 'RWF'
    });
}

function updateQuantity(productId, newQuantity) {
    if (newQuantity < 1) {
        removeFromCart(productId);
        return;
    }
    
    // Update via API
    fetch('<?php echo BASE_URL; ?>api/cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'update',
            cart_item_id: productId, // This should be cart item ID, not product ID
            quantity: parseInt(newQuantity)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadCartItems(); // Reload cart
            updateCartCount(); // Update header cart count
            showNotification('Cart updated successfully', 'success');
        } else {
            showNotification(data.message || 'Error updating cart', 'error');
        }
    })
    .catch(error => {
        console.error('Error updating cart:', error);
        showNotification('Error updating cart', 'error');
    });
}

function removeFromCart(productId) {
    if (!confirm('Are you sure you want to remove this item from your cart?')) {
        return;
    }
    
    fetch('<?php echo BASE_URL; ?>api/cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'remove',
            product_id: productId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadCartItems(); // Reload cart
            updateCartCount(); // Update header cart count
            showNotification('Item removed from cart', 'info');
        } else {
            showNotification(data.message || 'Error removing item', 'error');
        }
    })
    .catch(error => {
        console.error('Error removing from cart:', error);
        showNotification('Error removing item', 'error');
    });
}

function updateCartSummary(summary) {
    const subtotal = summary.total_amount || 0;
    const shipping = subtotal > 0 ? (subtotal >= 50000 ? 0 : 2000) : 0; // Free shipping over 50k
    const tax = Math.round(subtotal * 0.18); // 18% tax
    const total = subtotal + shipping + tax;
    
    document.getElementById('cart-subtotal').textContent = `RWF ${formatPrice(subtotal)}`;
    document.getElementById('cart-shipping').textContent = shipping === 0 ? 'FREE' : `RWF ${formatPrice(shipping)}`;
    document.getElementById('cart-tax').textContent = `RWF ${formatPrice(tax)}`;
    document.getElementById('cart-total').innerHTML = `<strong>RWF ${formatPrice(total)}</strong>`;
    
    // Enable/disable checkout button
    const checkoutBtn = document.getElementById('checkout-btn');
    if (subtotal > 0) {
        checkoutBtn.disabled = false;
        checkoutBtn.onclick = () => proceedToCheckout();
    } else {
        checkoutBtn.disabled = true;
    }
}

function loadRecommendedProducts() {
    // Simulate loading recommended products
    setTimeout(() => {
        document.getElementById('recommended-products').innerHTML = `
            <div class="recommended-item">
                <img src="<?php echo BASE_URL; ?>assets/images/placeholder.jpg" alt="Product">
                <div class="recommended-item-details">
                    <div class="recommended-item-title">Wireless Headphones</div>
                    <div class="recommended-item-price">RWF 45,000</div>
                </div>
            </div>
            <div class="recommended-item">
                <img src="<?php echo BASE_URL; ?>assets/images/placeholder.jpg" alt="Product">
                <div class="recommended-item-details">
                    <div class="recommended-item-title">Phone Case</div>
                    <div class="recommended-item-price">RWF 8,500</div>
                </div>
            </div>
        `;
    }, 1500);
}

function applyPromoCode() {
    const promoCode = document.getElementById('promo-code-input').value.trim();
    
    if (!promoCode) {
        showNotification('Please enter a promo code', 'warning');
        return;
    }
    
    // Simulate promo code validation
    showNotification('Promo code feature coming soon!', 'info');
}

function proceedToCheckout() {
    // Check if user is logged in
    <?php if (!isset($_SESSION['user_id'])): ?>
        showNotification('Please login to proceed to checkout', 'warning');
        setTimeout(() => {
            window.location.href = '<?php echo BASE_URL; ?>login';
        }, 1500);
        return;
    <?php endif; ?>
    
    window.location.href = '<?php echo BASE_URL; ?>checkout';
}

function formatPrice(price) {
    return new Intl.NumberFormat('en-RW').format(price);
}

function updateCartCount() {
    // This function is defined in main.js
    if (typeof window.updateCartCount === 'function') {
        window.updateCartCount();
    }
}

// Show notification function
function showNotification(message, type = 'info', duration = 5000) {
    let container = document.getElementById('notification-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notification-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
        `;
        document.body.appendChild(container);
    }
    
    const notification = document.createElement('div');
    
    const colors = {
        success: '#4CAF50',
        error: '#f44336',
        warning: '#ff9800',
        info: '#2196F3'
    };
    
    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };
    
    notification.style.cssText = `
        background: ${colors[type]};
        color: white;
        padding: 15px 20px;
        margin-bottom: 10px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        display: flex;
        align-items: center;
        gap: 10px;
        animation: slideIn 0.3s ease;
        cursor: pointer;
    `;
    
    notification.innerHTML = `
        <i class="${icons[type]}"></i>
        <span>${message}</span>
        <i class="fas fa-times" style="margin-left: auto; cursor: pointer;"></i>
    `;
    
    notification.addEventListener('click', () => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    });
    
    container.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }
    }, duration);
}
</script>

<?php include 'includes/footer.php'; ?>
