<?php
$current_page = 'vendor-login';
$page_title = 'Vendor Login';
$page_description = 'Login to your vendor dashboard - Musanze Marketplace';

// Include vendor authentication
require_once '../../includes/vendor-auth.php';

// Redirect if already logged in as vendor
if ($vendorAuth->isVendor()) {
    header('Location: ' . BASE_URL . 'vendor-dashboard');
    exit;
}

// Get redirect URL if provided
$redirect_url = $_GET['redirect'] ?? '';

include '../../includes/header.php';
?>

<div class="vendor-login-container">
    <div class="container" style="padding: 3rem 0; min-height: 80vh; display: flex; align-items: center;">
        <div class="row justify-center w-100">
            <div class="col col-6" style="max-width: 500px;">
                <!-- Vendor Login Card -->
                <div class="card vendor-login-card">
                    <div class="card-header text-center">
                        <div class="vendor-logo mb-3">
                            <i class="fas fa-store" style="font-size: 3rem; color: var(--primary-green);"></i>
                        </div>
                        <h3 style="color: var(--primary-green); margin-bottom: 0.5rem;">Vendor Login</h3>
                        <p style="color: var(--gray-dark); margin: 0;">Access your business dashboard</p>
                    </div>
                    
                    <div class="card-body">
                        <!-- Security Alert -->
                        <div id="security-alert" class="alert alert-warning" style="display: none;">
                            <i class="fas fa-shield-alt"></i>
                            <span id="security-message"></span>
                        </div>
                        
                        <!-- Login Form -->
                        <form id="vendor-login-form">
                            <input type="hidden" name="csrf_token" value="<?php echo $vendorAuth->generateCSRFToken(); ?>">
                            <input type="hidden" name="redirect_url" value="<?php echo htmlspecialchars($redirect_url); ?>">
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-envelope"></i> Business Email Address
                                </label>
                                <input type="email" 
                                       name="email" 
                                       class="form-control" 
                                       placeholder="Enter your business email"
                                       required
                                       autocomplete="email">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-lock"></i> Password
                                </label>
                                <div class="password-input-container">
                                    <input type="password" 
                                           name="password" 
                                           class="form-control" 
                                           placeholder="Enter your password"
                                           id="password-input"
                                           required
                                           autocomplete="current-password">
                                    <button type="button" 
                                            class="password-toggle"
                                            onclick="togglePassword()"
                                            tabindex="-1">
                                        <i class="fas fa-eye" id="password-toggle-icon"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="form-group" style="display: flex; justify-content: space-between; align-items: center;">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="remember" value="1">
                                    <span class="checkmark"></span>
                                    Keep me logged in
                                </label>
                                <a href="<?php echo BASE_URL; ?>vendor/forgot-password" class="forgot-link">
                                    Forgot password?
                                </a>
                            </div>
                            
                            <!-- Login Button -->
                            <button type="submit" class="btn btn-primary w-100 btn-lg" id="login-btn">
                                <i class="fas fa-sign-in-alt"></i> Login to Dashboard
                            </button>
                        </form>
                        
                        <!-- Alternative Actions -->
                        <div class="login-alternatives text-center mt-4">
                            <div class="divider">
                                <span>New to Musanze Marketplace?</span>
                            </div>
                            
                            <a href="<?php echo BASE_URL; ?>vendor-register" class="btn btn-secondary w-100 mt-3">
                                <i class="fas fa-store"></i> Become a Vendor
                            </a>
                            
                            <div class="customer-link mt-3">
                                <p style="color: var(--gray-dark); font-size: 0.9rem;">
                                    Looking to shop? 
                                    <a href="<?php echo BASE_URL; ?>login" style="color: var(--primary-green);">
                                        Customer Login
                                    </a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Security Notice -->
                <div class="security-notice mt-3">
                    <div class="card" style="background: var(--light-green); border: 1px solid var(--secondary-green);">
                        <div class="card-body text-center" style="padding: 1.5rem;">
                            <i class="fas fa-shield-alt" style="color: var(--primary-green); font-size: 1.5rem; margin-bottom: 0.5rem;"></i>
                            <h6 style="color: var(--primary-green); margin-bottom: 0.5rem;">Secure Vendor Access</h6>
                            <p style="color: var(--gray-dark); font-size: 0.9rem; margin: 0;">
                                Your business data is protected with enterprise-grade security. 
                                We use encryption and secure protocols to keep your information safe.
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- Support Contact -->
                <div class="support-contact text-center mt-3">
                    <p style="color: var(--gray-dark); font-size: 0.9rem;">
                        Need help? Contact vendor support: 
                        <a href="mailto:<EMAIL>" style="color: var(--primary-green);">
                            <EMAIL>
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.vendor-login-container {
    background: linear-gradient(135deg, var(--light-green) 0%, var(--gray-light) 100%);
    min-height: 100vh;
}

.vendor-login-card {
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: none;
    border-radius: 15px;
    overflow: hidden;
}

.vendor-login-card .card-header {
    background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
    color: white;
    padding: 2rem;
    border-bottom: none;
}

.vendor-login-card .card-body {
    padding: 2rem;
}

.password-input-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--gray-dark);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: var(--transition);
}

.password-toggle:hover {
    background: var(--gray-light);
    color: var(--primary-green);
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--gray-dark);
}

.checkbox-label input[type="checkbox"] {
    margin-right: 0.5rem;
}

.forgot-link {
    color: var(--primary-green);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.forgot-link:hover {
    color: var(--secondary-green);
    text-decoration: underline;
}

.divider {
    position: relative;
    text-align: center;
    margin: 1.5rem 0;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--gray-medium);
}

.divider span {
    background: white;
    padding: 0 1rem;
    color: var(--gray-dark);
    font-size: 0.9rem;
}

.alert {
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.alert-danger {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
    font-weight: 600;
}

@media (max-width: 768px) {
    .vendor-login-container .container {
        padding: 2rem 1rem;
    }
    
    .col {
        max-width: none !important;
    }
    
    .vendor-login-card .card-header,
    .vendor-login-card .card-body {
        padding: 1.5rem;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('vendor-login-form');
    const loginBtn = document.getElementById('login-btn');
    
    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();
        handleVendorLogin();
    });
    
    // Auto-focus email field
    const emailInput = document.querySelector('input[name="email"]');
    if (emailInput) {
        emailInput.focus();
    }
});

function handleVendorLogin() {
    const form = document.getElementById('vendor-login-form');
    const formData = new FormData(form);
    const loginBtn = document.getElementById('login-btn');
    
    // Disable button and show loading
    loginBtn.disabled = true;
    loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging in...';
    
    // Hide any existing alerts
    hideAlert();
    
    const data = {
        action: 'login',
        email: formData.get('email'),
        password: formData.get('password'),
        remember: formData.get('remember') ? true : false,
        csrf_token: formData.get('csrf_token'),
        user_type: 'vendor' // Specify this is a vendor login
    };
    
    fetch('<?php echo BASE_URL; ?>api/auth.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Login successful! Redirecting to dashboard...', 'success');
            
            // Redirect after short delay
            setTimeout(() => {
                const redirectUrl = formData.get('redirect_url') || data.redirect || '<?php echo BASE_URL; ?>vendor-dashboard';
                window.location.href = redirectUrl;
            }, 1000);
        } else {
            // Handle different error types
            if (data.status === 'pending_verification') {
                showAlert(data.message, 'warning');
                setTimeout(() => {
                    window.location.href = '<?php echo BASE_URL; ?>vendor-verification';
                }, 3000);
            } else if (data.status === 'rejected') {
                showAlert(data.message, 'danger');
            } else {
                showAlert(data.message, 'danger');
            }
            
            // Re-enable button
            loginBtn.disabled = false;
            loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Login to Dashboard';
        }
    })
    .catch(error => {
        console.error('Login error:', error);
        showAlert('An error occurred. Please try again.', 'danger');
        
        // Re-enable button
        loginBtn.disabled = false;
        loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Login to Dashboard';
    });
}

function togglePassword() {
    const passwordInput = document.getElementById('password-input');
    const toggleIcon = document.getElementById('password-toggle-icon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleIcon.className = 'fas fa-eye';
    }
}

function showAlert(message, type) {
    const alertElement = document.getElementById('security-alert');
    const messageElement = document.getElementById('security-message');
    
    alertElement.className = `alert alert-${type}`;
    messageElement.textContent = message;
    alertElement.style.display = 'flex';
    
    // Auto-hide success messages
    if (type === 'success') {
        setTimeout(() => {
            hideAlert();
        }, 5000);
    }
}

function hideAlert() {
    const alertElement = document.getElementById('security-alert');
    alertElement.style.display = 'none';
}

// Handle browser back button
window.addEventListener('pageshow', function(event) {
    if (event.persisted) {
        // Page was loaded from cache, check if user is still logged in
        fetch('<?php echo BASE_URL; ?>api/auth.php?action=status')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.authenticated && data.user.user_type === 'vendor') {
                    window.location.href = '<?php echo BASE_URL; ?>vendor-dashboard';
                }
            })
            .catch(error => {
                console.error('Auth check error:', error);
            });
    }
});
</script>

<?php include '../../includes/footer.php'; ?>
