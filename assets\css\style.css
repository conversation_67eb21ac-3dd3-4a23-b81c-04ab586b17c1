/**
 * Musanze Marketplace - Main Stylesheet
 * Professional Product Price Comparison Platform
 * Color Scheme: Green (#2E7D32, #4CAF50), White (#FFFFFF), Black (#212121)
 */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #212121;
    background-color: #FFFFFF;
}

/* CSS Variables for Color Scheme */
:root {
    --primary-green: #2E7D32;
    --secondary-green: #4CAF50;
    --accent-green: #66BB6A;
    --light-green: #E8F5E8;
    --white: #FFFFFF;
    --black: #212121;
    --gray-light: #F5F5F5;
    --gray-medium: #E0E0E0;
    --gray-dark: #757575;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-hover: 0 4px 20px rgba(0,0,0,0.15);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--black);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
    color: var(--gray-dark);
}

a {
    color: var(--primary-green);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--secondary-green);
}

/* Layout Components */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    padding: 0 15px;
}

.col-1 { flex: 0 0 8.333333%; }
.col-2 { flex: 0 0 16.666667%; }
.col-3 { flex: 0 0 25%; }
.col-4 { flex: 0 0 33.333333%; }
.col-6 { flex: 0 0 50%; }
.col-8 { flex: 0 0 66.666667%; }
.col-9 { flex: 0 0 75%; }
.col-12 { flex: 0 0 100%; }

/* Header */
.header {
    background: var(--white);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-top {
    background: var(--primary-green);
    color: var(--white);
    padding: 8px 0;
    font-size: 0.9rem;
}

.header-main {
    padding: 1rem 0;
}

.logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-green);
}

.search-bar {
    flex: 1;
    max-width: 600px;
    margin: 0 2rem;
    position: relative;
}

.search-input {
    width: 100%;
    padding: 12px 50px 12px 20px;
    border: 2px solid var(--gray-medium);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
}

.search-btn {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--primary-green);
    color: var(--white);
    border: none;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.search-btn:hover {
    background: var(--secondary-green);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.header-link:hover {
    background: var(--light-green);
}

.cart-count {
    background: var(--secondary-green);
    color: var(--white);
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 0.8rem;
    min-width: 20px;
    text-align: center;
}

/* Navigation */
.navbar {
    background: var(--gray-light);
    padding: 0.5rem 0;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.nav-link:hover,
.nav-link.active {
    background: var(--primary-green);
    color: var(--white);
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-green);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--secondary-green);
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.btn-secondary {
    background: var(--white);
    color: var(--primary-green);
    border: 2px solid var(--primary-green);
}

.btn-secondary:hover {
    background: var(--primary-green);
    color: var(--white);
}

.btn-success {
    background: var(--secondary-green);
    color: var(--white);
}

.btn-success:hover {
    background: var(--accent-green);
}

.btn-sm {
    padding: 8px 16px;
    font-size: 0.9rem;
}

.btn-lg {
    padding: 16px 32px;
    font-size: 1.1rem;
}

/* Cards */
.card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.card-header {
    padding: 1.5rem;
    background: var(--gray-light);
    border-bottom: 1px solid var(--gray-medium);
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    padding: 1rem 1.5rem;
    background: var(--gray-light);
    border-top: 1px solid var(--gray-medium);
}

/* Product Cards */
.product-card {
    margin-bottom: 2rem;
}

.product-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: var(--secondary-green);
    color: var(--white);
    padding: 4px 8px;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 500;
}

.product-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    opacity: 0;
    transition: var(--transition);
}

.product-card:hover .product-actions {
    opacity: 1;
}

.action-btn {
    background: var(--white);
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.action-btn:hover {
    background: var(--primary-green);
    color: var(--white);
}

.product-info {
    padding: 1rem;
}

.product-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--black);
}

.product-vendor {
    color: var(--gray-dark);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.stars {
    color: #FFC107;
}

.product-price {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.current-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-green);
}

.original-price {
    font-size: 1rem;
    color: var(--gray-dark);
    text-decoration: line-through;
}

.discount {
    background: var(--secondary-green);
    color: var(--white);
    padding: 2px 6px;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--black);
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 2px solid var(--gray-medium);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: 40px;
}

/* Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }
.p-4 { padding: 2rem; }

.d-flex { display: flex; }
.d-block { display: block; }
.d-none { display: none; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .header-main {
        flex-direction: column;
        gap: 1rem;
    }
    
    .search-bar {
        margin: 0;
        max-width: none;
    }
    
    .nav-menu {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .row {
        margin: 0 -10px;
    }
    
    .col {
        padding: 0 10px;
    }
    
    .col-sm-12 { flex: 0 0 100%; }
    .col-sm-6 { flex: 0 0 50%; }
    .col-sm-4 { flex: 0 0 33.333333%; }
    .col-sm-3 { flex: 0 0 25%; }
}

@media (max-width: 480px) {
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }
    
    .product-image {
        height: 200px;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}
