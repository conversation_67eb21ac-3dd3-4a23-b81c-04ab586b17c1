<?php
$current_page = 'login';
$page_title = 'Login';
$page_description = 'Login to your Musanze Marketplace account';

// Redirect if already logged in
session_start();
if (isset($_SESSION['user_id'])) {
    $redirect_url = $_SESSION['user_type'] === 'vendor' ? 'vendor-dashboard' : 
                   ($_SESSION['user_type'] === 'admin' ? 'admin' : 'dashboard');
    header('Location: ' . BASE_URL . $redirect_url);
    exit;
}

include '../includes/header.php';
?>

<div class="container" style="padding: 3rem 0;">
    <div class="row justify-center">
        <div class="col col-6" style="max-width: 500px;">
            <div class="card">
                <div class="card-header text-center">
                    <h3><i class="fas fa-sign-in-alt" style="color: var(--primary-green);"></i> Login to Your Account</h3>
                    <p style="color: var(--gray-dark); margin: 0;">Welcome back to Musanze Marketplace</p>
                </div>
                
                <div class="card-body">
                    <form id="login-form">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-envelope"></i> Email Address
                            </label>
                            <input type="email" 
                                   name="email" 
                                   class="form-control" 
                                   placeholder="Enter your email"
                                   required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-lock"></i> Password
                            </label>
                            <div style="position: relative;">
                                <input type="password" 
                                       name="password" 
                                       class="form-control" 
                                       placeholder="Enter your password"
                                       id="password-input"
                                       required>
                                <button type="button" 
                                        class="password-toggle"
                                        onclick="togglePassword()"
                                        style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); background: none; border: none; color: var(--gray-dark); cursor: pointer;">
                                    <i class="fas fa-eye" id="password-toggle-icon"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="form-group" style="display: flex; justify-content: space-between; align-items: center;">
                            <label style="display: flex; align-items: center; margin: 0;">
                                <input type="checkbox" name="remember" style="margin-right: 0.5rem;">
                                Remember me
                            </label>
                            <a href="<?php echo BASE_URL; ?>forgot-password" style="color: var(--primary-green);">
                                Forgot password?
                            </a>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 btn-lg" id="login-btn">
                            <i class="fas fa-sign-in-alt"></i> Login
                        </button>
                    </form>
                    
                    <div class="text-center mt-3">
                        <p style="color: var(--gray-dark);">
                            Don't have an account? 
                            <a href="<?php echo BASE_URL; ?>register" style="color: var(--primary-green); font-weight: 500;">
                                Register here
                            </a>
                        </p>
                        <p style="color: var(--gray-dark);">
                            Want to sell your products? 
                            <a href="<?php echo BASE_URL; ?>vendor-register" style="color: var(--secondary-green); font-weight: 500;">
                                Become a Vendor
                            </a>
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Demo Accounts -->
            <div class="card mt-3" style="background: var(--light-green);">
                <div class="card-header">
                    <h6><i class="fas fa-info-circle"></i> Demo Accounts</h6>
                </div>
                <div class="card-body">
                    <p style="margin-bottom: 1rem; font-size: 0.9rem;">Try the platform with these demo accounts:</p>
                    
                    <div class="demo-accounts" style="display: grid; gap: 1rem;">
                        <div class="demo-account" style="background: var(--white); padding: 1rem; border-radius: var(--border-radius); border: 1px solid var(--gray-medium);">
                            <strong>Customer Account</strong><br>
                            <small>Email: <EMAIL></small><br>
                            <small>Password: demo123</small>
                            <button class="btn btn-sm btn-secondary mt-1" onclick="fillDemoCredentials('<EMAIL>', 'demo123')">
                                Use This Account
                            </button>
                        </div>
                        
                        <div class="demo-account" style="background: var(--white); padding: 1rem; border-radius: var(--border-radius); border: 1px solid var(--gray-medium);">
                            <strong>Vendor Account</strong><br>
                            <small>Email: <EMAIL></small><br>
                            <small>Password: demo123</small>
                            <button class="btn btn-sm btn-secondary mt-1" onclick="fillDemoCredentials('<EMAIL>', 'demo123')">
                                Use This Account
                            </button>
                        </div>
                        
                        <div class="demo-account" style="background: var(--white); padding: 1rem; border-radius: var(--border-radius); border: 1px solid var(--gray-medium);">
                            <strong>Admin Account</strong><br>
                            <small>Email: <EMAIL></small><br>
                            <small>Password: admin123</small>
                            <button class="btn btn-sm btn-secondary mt-1" onclick="fillDemoCredentials('<EMAIL>', 'admin123')">
                                Use This Account
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('login-form');
    const loginBtn = document.getElementById('login-btn');
    
    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();
        handleLogin();
    });
});

function handleLogin() {
    const form = document.getElementById('login-form');
    const formData = new FormData(form);
    const loginBtn = document.getElementById('login-btn');
    
    // Disable button and show loading
    loginBtn.disabled = true;
    loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging in...';
    
    const data = {
        action: 'login',
        email: formData.get('email'),
        password: formData.get('password'),
        remember: formData.get('remember') ? true : false
    };
    
    fetch('<?php echo BASE_URL; ?>api/auth.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            
            // Redirect after short delay
            setTimeout(() => {
                window.location.href = data.redirect || '<?php echo BASE_URL; ?>';
            }, 1000);
        } else {
            showNotification(data.message, 'error');
            
            // Re-enable button
            loginBtn.disabled = false;
            loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Login';
        }
    })
    .catch(error => {
        console.error('Login error:', error);
        showNotification('An error occurred. Please try again.', 'error');
        
        // Re-enable button
        loginBtn.disabled = false;
        loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Login';
    });
}

function togglePassword() {
    const passwordInput = document.getElementById('password-input');
    const toggleIcon = document.getElementById('password-toggle-icon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleIcon.className = 'fas fa-eye';
    }
}

function fillDemoCredentials(email, password) {
    document.querySelector('input[name="email"]').value = email;
    document.querySelector('input[name="password"]').value = password;
    showNotification('Demo credentials filled. Click Login to continue.', 'info');
}

// Show notification function (if not already defined)
function showNotification(message, type = 'info', duration = 5000) {
    // Create notification container if it doesn't exist
    let container = document.getElementById('notification-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notification-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
        `;
        document.body.appendChild(container);
    }
    
    const notification = document.createElement('div');
    
    const colors = {
        success: '#4CAF50',
        error: '#f44336',
        warning: '#ff9800',
        info: '#2196F3'
    };
    
    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };
    
    notification.style.cssText = `
        background: ${colors[type]};
        color: white;
        padding: 15px 20px;
        margin-bottom: 10px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        display: flex;
        align-items: center;
        gap: 10px;
        animation: slideIn 0.3s ease;
        cursor: pointer;
    `;
    
    notification.innerHTML = `
        <i class="${icons[type]}"></i>
        <span>${message}</span>
        <i class="fas fa-times" style="margin-left: auto; cursor: pointer;"></i>
    `;
    
    // Add click to close
    notification.addEventListener('click', () => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    });
    
    container.appendChild(notification);
    
    // Auto remove after duration
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }
    }, duration);
}
</script>

<style>
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.demo-accounts {
    font-size: 0.9rem;
}

.demo-account {
    transition: var(--transition);
}

.demo-account:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

@media (max-width: 768px) {
    .container {
        padding: 2rem 0;
    }
    
    .col {
        max-width: none !important;
    }
    
    .demo-accounts {
        grid-template-columns: 1fr;
    }
}
</style>

<?php include '../includes/footer.php'; ?>
