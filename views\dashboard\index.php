<?php
$current_page = 'dashboard';
$page_title = 'Customer Dashboard';
$page_description = 'Manage your account, orders, and preferences';

session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ' . BASE_URL . 'login');
    exit;
}

// Redirect vendors and admins to their respective dashboards
if ($_SESSION['user_type'] === 'vendor') {
    header('Location: ' . BASE_URL . 'vendor-dashboard');
    exit;
} elseif ($_SESSION['user_type'] === 'admin') {
    header('Location: ' . BASE_URL . 'admin');
    exit;
}

include '../includes/header.php';
?>

<div class="container" style="padding: 2rem 0;">
    <!-- Dashboard Header -->
    <div class="dashboard-header mb-4">
        <div class="row align-center justify-between">
            <div class="col">
                <h2>
                    <i class="fas fa-tachometer-alt" style="color: var(--primary-green);"></i>
                    Welcome back, <?php echo htmlspecialchars($_SESSION['first_name']); ?>!
                </h2>
                <p style="color: var(--gray-dark); margin: 0;">Manage your account and track your orders</p>
            </div>
            <div class="col text-right">
                <span class="badge" style="background: var(--secondary-green); color: white; padding: 0.5rem 1rem; border-radius: var(--border-radius);">
                    Customer Account
                </span>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col col-3">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-user"></i> Account Menu</h5>
                </div>
                <div class="card-body" style="padding: 0;">
                    <nav class="dashboard-nav">
                        <a href="#overview" class="nav-item active" onclick="showSection('overview')">
                            <i class="fas fa-chart-line"></i> Overview
                        </a>
                        <a href="#orders" class="nav-item" onclick="showSection('orders')">
                            <i class="fas fa-shopping-bag"></i> My Orders
                        </a>
                        <a href="#wishlist" class="nav-item" onclick="showSection('wishlist')">
                            <i class="fas fa-heart"></i> Wishlist
                        </a>
                        <a href="#profile" class="nav-item" onclick="showSection('profile')">
                            <i class="fas fa-user-edit"></i> Profile Settings
                        </a>
                        <a href="#addresses" class="nav-item" onclick="showSection('addresses')">
                            <i class="fas fa-map-marker-alt"></i> Addresses
                        </a>
                        <a href="#security" class="nav-item" onclick="showSection('security')">
                            <i class="fas fa-shield-alt"></i> Security
                        </a>
                    </nav>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col col-9">
            <!-- Overview Section -->
            <div id="overview-section" class="dashboard-section">
                <!-- Quick Stats -->
                <div class="row mb-4">
                    <div class="col col-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-shopping-bag"></i>
                            </div>
                            <div class="stat-content">
                                <h4 id="total-orders">0</h4>
                                <p>Total Orders</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col col-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-heart"></i>
                            </div>
                            <div class="stat-content">
                                <h4 id="wishlist-items">0</h4>
                                <p>Wishlist Items</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col col-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="stat-content">
                                <h4 id="cart-items">0</h4>
                                <p>Cart Items</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col col-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="stat-content">
                                <h4 id="reviews-count">0</h4>
                                <p>Reviews Written</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Orders -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-clock"></i> Recent Orders</h5>
                    </div>
                    <div class="card-body">
                        <div id="recent-orders">
                            <div class="text-center" style="padding: 2rem;">
                                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: var(--primary-green);"></i>
                                <p style="margin-top: 1rem;">Loading recent orders...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="quick-actions">
                            <a href="<?php echo BASE_URL; ?>products" class="action-btn">
                                <i class="fas fa-search"></i>
                                <span>Browse Products</span>
                            </a>
                            <a href="<?php echo BASE_URL; ?>cart" class="action-btn">
                                <i class="fas fa-shopping-cart"></i>
                                <span>View Cart</span>
                            </a>
                            <a href="#wishlist" class="action-btn" onclick="showSection('wishlist')">
                                <i class="fas fa-heart"></i>
                                <span>My Wishlist</span>
                            </a>
                            <a href="#orders" class="action-btn" onclick="showSection('orders')">
                                <i class="fas fa-list"></i>
                                <span>Order History</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Orders Section -->
            <div id="orders-section" class="dashboard-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-shopping-bag"></i> My Orders</h5>
                    </div>
                    <div class="card-body">
                        <div id="orders-content">
                            <div class="text-center" style="padding: 2rem;">
                                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: var(--primary-green);"></i>
                                <p style="margin-top: 1rem;">Loading orders...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Wishlist Section -->
            <div id="wishlist-section" class="dashboard-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-heart"></i> My Wishlist</h5>
                    </div>
                    <div class="card-body">
                        <div id="wishlist-content">
                            <div class="text-center" style="padding: 2rem;">
                                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: var(--primary-green);"></i>
                                <p style="margin-top: 1rem;">Loading wishlist...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Section -->
            <div id="profile-section" class="dashboard-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-user-edit"></i> Profile Settings</h5>
                    </div>
                    <div class="card-body">
                        <form id="profile-form">
                            <div class="row">
                                <div class="col col-6">
                                    <div class="form-group">
                                        <label class="form-label">First Name</label>
                                        <input type="text" name="first_name" class="form-control" 
                                               value="<?php echo htmlspecialchars($_SESSION['first_name']); ?>">
                                    </div>
                                </div>
                                <div class="col col-6">
                                    <div class="form-group">
                                        <label class="form-label">Last Name</label>
                                        <input type="text" name="last_name" class="form-control" 
                                               value="<?php echo htmlspecialchars($_SESSION['last_name']); ?>">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Email Address</label>
                                <input type="email" name="email" class="form-control" 
                                       value="<?php echo htmlspecialchars($_SESSION['email']); ?>">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Phone Number</label>
                                <input type="tel" name="phone" class="form-control" id="phone-input">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Address</label>
                                <textarea name="address" class="form-control" rows="3" id="address-input"></textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Profile
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Addresses Section -->
            <div id="addresses-section" class="dashboard-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <div class="row align-center justify-between">
                            <div class="col">
                                <h5><i class="fas fa-map-marker-alt"></i> Saved Addresses</h5>
                            </div>
                            <div class="col text-right">
                                <button class="btn btn-primary btn-sm" onclick="showAddAddressForm()">
                                    <i class="fas fa-plus"></i> Add Address
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="addresses-content">
                            <div class="text-center" style="padding: 2rem;">
                                <i class="fas fa-map-marker-alt" style="font-size: 3rem; color: var(--gray-medium);"></i>
                                <h5>No saved addresses</h5>
                                <p style="color: var(--gray-dark);">Add your delivery addresses for faster checkout</p>
                                <button class="btn btn-primary" onclick="showAddAddressForm()">
                                    <i class="fas fa-plus"></i> Add Your First Address
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Section -->
            <div id="security-section" class="dashboard-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-shield-alt"></i> Security Settings</h5>
                    </div>
                    <div class="card-body">
                        <form id="password-form">
                            <div class="form-group">
                                <label class="form-label">Current Password</label>
                                <input type="password" name="current_password" class="form-control" required>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">New Password</label>
                                <input type="password" name="new_password" class="form-control" required>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Confirm New Password</label>
                                <input type="password" name="confirm_password" class="form-control" required>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-key"></i> Change Password
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.dashboard-nav {
    display: flex;
    flex-direction: column;
}

.dashboard-nav .nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    color: var(--gray-dark);
    text-decoration: none;
    border-bottom: 1px solid var(--gray-light);
    transition: var(--transition);
}

.dashboard-nav .nav-item:hover,
.dashboard-nav .nav-item.active {
    background: var(--light-green);
    color: var(--primary-green);
}

.dashboard-nav .nav-item:last-child {
    border-bottom: none;
}

.stat-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.stat-icon {
    background: var(--light-green);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-green);
    font-size: 1.5rem;
}

.stat-content h4 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--black);
}

.stat-content p {
    margin: 0;
    color: var(--gray-dark);
    font-size: 0.9rem;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1.5rem;
    background: var(--gray-light);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--gray-dark);
    transition: var(--transition);
}

.action-btn:hover {
    background: var(--light-green);
    color: var(--primary-green);
    transform: translateY(-2px);
}

.action-btn i {
    font-size: 1.5rem;
}

@media (max-width: 768px) {
    .row {
        flex-direction: column;
    }
    
    .col {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .dashboard-nav {
        flex-direction: row;
        overflow-x: auto;
    }
    
    .dashboard-nav .nav-item {
        white-space: nowrap;
        border-bottom: none;
        border-right: 1px solid var(--gray-light);
    }
    
    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
    loadUserProfile();
});

function showSection(sectionName) {
    // Hide all sections
    const sections = document.querySelectorAll('.dashboard-section');
    sections.forEach(section => section.style.display = 'none');
    
    // Show selected section
    document.getElementById(sectionName + '-section').style.display = 'block';
    
    // Update navigation
    const navItems = document.querySelectorAll('.dashboard-nav .nav-item');
    navItems.forEach(item => item.classList.remove('active'));
    event.target.classList.add('active');
    
    // Load section-specific data
    switch(sectionName) {
        case 'orders':
            loadOrders();
            break;
        case 'wishlist':
            loadWishlist();
            break;
    }
}

function loadDashboardData() {
    // Load quick stats
    loadQuickStats();
    loadRecentOrders();
}

function loadQuickStats() {
    // This would typically fetch from API
    // For now, using placeholder data
    document.getElementById('total-orders').textContent = '12';
    document.getElementById('wishlist-items').textContent = '8';
    document.getElementById('cart-items').textContent = '3';
    document.getElementById('reviews-count').textContent = '5';
}

function loadRecentOrders() {
    // Simulate loading recent orders
    setTimeout(() => {
        document.getElementById('recent-orders').innerHTML = `
            <div class="order-item" style="display: flex; justify-content: space-between; align-items: center; padding: 1rem; border: 1px solid var(--gray-medium); border-radius: var(--border-radius); margin-bottom: 1rem;">
                <div>
                    <h6 style="margin: 0;">Order #ORD-2024-001</h6>
                    <p style="margin: 0; color: var(--gray-dark); font-size: 0.9rem;">3 items • RWF 125,000</p>
                </div>
                <div>
                    <span class="badge" style="background: var(--secondary-green); color: white; padding: 0.25rem 0.5rem; border-radius: var(--border-radius); font-size: 0.8rem;">Delivered</span>
                </div>
            </div>
            <div class="text-center">
                <a href="#orders" onclick="showSection('orders')" class="btn btn-secondary">
                    <i class="fas fa-list"></i> View All Orders
                </a>
            </div>
        `;
    }, 1000);
}

function loadOrders() {
    document.getElementById('orders-content').innerHTML = `
        <div class="text-center" style="padding: 3rem;">
            <i class="fas fa-shopping-bag" style="font-size: 3rem; color: var(--gray-medium);"></i>
            <h5>No orders yet</h5>
            <p style="color: var(--gray-dark);">Start shopping to see your orders here</p>
            <a href="<?php echo BASE_URL; ?>products" class="btn btn-primary">
                <i class="fas fa-search"></i> Browse Products
            </a>
        </div>
    `;
}

function loadWishlist() {
    document.getElementById('wishlist-content').innerHTML = `
        <div class="text-center" style="padding: 3rem;">
            <i class="fas fa-heart" style="font-size: 3rem; color: var(--gray-medium);"></i>
            <h5>Your wishlist is empty</h5>
            <p style="color: var(--gray-dark);">Save items you love to your wishlist</p>
            <a href="<?php echo BASE_URL; ?>products" class="btn btn-primary">
                <i class="fas fa-search"></i> Discover Products
            </a>
        </div>
    `;
}

function loadUserProfile() {
    // This would typically fetch user data from API
    // For now, using session data that's already available
}

function showAddAddressForm() {
    showNotification('Add address feature coming soon!', 'info');
}

// Show notification function
function showNotification(message, type = 'info', duration = 5000) {
    let container = document.getElementById('notification-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notification-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
        `;
        document.body.appendChild(container);
    }
    
    const notification = document.createElement('div');
    
    const colors = {
        success: '#4CAF50',
        error: '#f44336',
        warning: '#ff9800',
        info: '#2196F3'
    };
    
    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };
    
    notification.style.cssText = `
        background: ${colors[type]};
        color: white;
        padding: 15px 20px;
        margin-bottom: 10px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        display: flex;
        align-items: center;
        gap: 10px;
        animation: slideIn 0.3s ease;
        cursor: pointer;
    `;
    
    notification.innerHTML = `
        <i class="${icons[type]}"></i>
        <span>${message}</span>
        <i class="fas fa-times" style="margin-left: auto; cursor: pointer;"></i>
    `;
    
    notification.addEventListener('click', () => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    });
    
    container.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }
    }, duration);
}
</script>

<?php include '../includes/footer.php'; ?>
