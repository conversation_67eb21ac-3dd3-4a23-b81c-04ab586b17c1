<?php
/**
 * Shopping Cart API Endpoint
 * Handles cart operations for logged-in users
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

session_start();

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetRequest($db);
            break;
        case 'POST':
            handlePostRequest($db);
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}

function handleGetRequest($db) {
    $action = $_GET['action'] ?? 'list';
    
    switch ($action) {
        case 'list':
            getCartItems($db);
            break;
        case 'count':
            getCartCount($db);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
}

function handlePostRequest($db) {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'add':
            addToCart($db, $input);
            break;
        case 'update':
            updateCartItem($db, $input);
            break;
        case 'remove':
            removeFromCart($db, $input);
            break;
        case 'clear':
            clearCart($db);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
}

function getCartItems($db) {
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'message' => 'User not logged in']);
        return;
    }
    
    $user_id = $_SESSION['user_id'];
    
    $query = "
        SELECT 
            ci.*,
            p.name as product_name,
            p.price as product_price,
            p.stock_quantity,
            p.slug as product_slug,
            v.first_name as vendor_first_name,
            v.last_name as vendor_last_name,
            vp.business_name as vendor_business_name,
            (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as product_image
        FROM cart_items ci
        LEFT JOIN products p ON ci.product_id = p.id
        LEFT JOIN users v ON p.vendor_id = v.id
        LEFT JOIN vendor_profiles vp ON v.id = vp.user_id
        WHERE ci.user_id = :user_id AND p.status = 'active'
        ORDER BY ci.created_at DESC
    ";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->execute();
    
    $cart_items = $stmt->fetchAll();
    
    $formatted_items = [];
    $total_amount = 0;
    $total_items = 0;
    
    foreach ($cart_items as $item) {
        $item_total = $item['product_price'] * $item['quantity'];
        $total_amount += $item_total;
        $total_items += $item['quantity'];
        
        $formatted_items[] = [
            'id' => (int)$item['id'],
            'product_id' => (int)$item['product_id'],
            'product_name' => $item['product_name'],
            'product_slug' => $item['product_slug'],
            'product_price' => (float)$item['product_price'],
            'quantity' => (int)$item['quantity'],
            'item_total' => $item_total,
            'stock_quantity' => (int)$item['stock_quantity'],
            'vendor_name' => $item['vendor_business_name'] ?: 
                           ($item['vendor_first_name'] . ' ' . $item['vendor_last_name']),
            'product_image' => $item['product_image'] ? BASE_URL . 'uploads/' . $item['product_image'] : null,
            'added_at' => $item['created_at']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'items' => $formatted_items,
        'summary' => [
            'total_items' => $total_items,
            'total_amount' => $total_amount,
            'currency' => 'RWF'
        ]
    ]);
}

function getCartCount($db) {
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => true, 'count' => 0]);
        return;
    }
    
    $user_id = $_SESSION['user_id'];
    
    $query = "
        SELECT COALESCE(SUM(ci.quantity), 0) as total_count
        FROM cart_items ci
        LEFT JOIN products p ON ci.product_id = p.id
        WHERE ci.user_id = :user_id AND p.status = 'active'
    ";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->execute();
    
    $result = $stmt->fetch();
    $count = $result ? (int)$result['total_count'] : 0;
    
    echo json_encode([
        'success' => true,
        'count' => $count
    ]);
}

function addToCart($db, $input) {
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'message' => 'User not logged in']);
        return;
    }
    
    $user_id = $_SESSION['user_id'];
    $product_id = $input['product_id'] ?? 0;
    $quantity = max(1, intval($input['quantity'] ?? 1));
    
    if (!$product_id) {
        echo json_encode(['success' => false, 'message' => 'Product ID is required']);
        return;
    }
    
    // Check if product exists and is active
    $product_query = "SELECT id, name, stock_quantity FROM products WHERE id = :product_id AND status = 'active'";
    $product_stmt = $db->prepare($product_query);
    $product_stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
    $product_stmt->execute();
    
    $product = $product_stmt->fetch();
    
    if (!$product) {
        echo json_encode(['success' => false, 'message' => 'Product not found or unavailable']);
        return;
    }
    
    // Check if item already exists in cart
    $check_query = "SELECT id, quantity FROM cart_items WHERE user_id = :user_id AND product_id = :product_id";
    $check_stmt = $db->prepare($check_query);
    $check_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $check_stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
    $check_stmt->execute();
    
    $existing_item = $check_stmt->fetch();
    
    if ($existing_item) {
        // Update existing item
        $new_quantity = $existing_item['quantity'] + $quantity;
        
        // Check stock availability
        if ($new_quantity > $product['stock_quantity']) {
            echo json_encode([
                'success' => false, 
                'message' => 'Not enough stock available. Available: ' . $product['stock_quantity']
            ]);
            return;
        }
        
        $update_query = "UPDATE cart_items SET quantity = :quantity, updated_at = CURRENT_TIMESTAMP WHERE id = :id";
        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':quantity', $new_quantity, PDO::PARAM_INT);
        $update_stmt->bindParam(':id', $existing_item['id'], PDO::PARAM_INT);
        
        if ($update_stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Cart updated successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to update cart']);
        }
    } else {
        // Check stock availability
        if ($quantity > $product['stock_quantity']) {
            echo json_encode([
                'success' => false, 
                'message' => 'Not enough stock available. Available: ' . $product['stock_quantity']
            ]);
            return;
        }
        
        // Add new item
        $insert_query = "INSERT INTO cart_items (user_id, product_id, quantity) VALUES (:user_id, :product_id, :quantity)";
        $insert_stmt = $db->prepare($insert_query);
        $insert_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $insert_stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
        $insert_stmt->bindParam(':quantity', $quantity, PDO::PARAM_INT);
        
        if ($insert_stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Product added to cart successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to add product to cart']);
        }
    }
}

function updateCartItem($db, $input) {
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'message' => 'User not logged in']);
        return;
    }
    
    $user_id = $_SESSION['user_id'];
    $cart_item_id = $input['cart_item_id'] ?? 0;
    $quantity = max(1, intval($input['quantity'] ?? 1));
    
    if (!$cart_item_id) {
        echo json_encode(['success' => false, 'message' => 'Cart item ID is required']);
        return;
    }
    
    // Verify cart item belongs to user and get product info
    $check_query = "
        SELECT ci.id, p.stock_quantity 
        FROM cart_items ci
        LEFT JOIN products p ON ci.product_id = p.id
        WHERE ci.id = :cart_item_id AND ci.user_id = :user_id AND p.status = 'active'
    ";
    $check_stmt = $db->prepare($check_query);
    $check_stmt->bindParam(':cart_item_id', $cart_item_id, PDO::PARAM_INT);
    $check_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $check_stmt->execute();
    
    $cart_item = $check_stmt->fetch();
    
    if (!$cart_item) {
        echo json_encode(['success' => false, 'message' => 'Cart item not found']);
        return;
    }
    
    // Check stock availability
    if ($quantity > $cart_item['stock_quantity']) {
        echo json_encode([
            'success' => false, 
            'message' => 'Not enough stock available. Available: ' . $cart_item['stock_quantity']
        ]);
        return;
    }
    
    // Update quantity
    $update_query = "UPDATE cart_items SET quantity = :quantity, updated_at = CURRENT_TIMESTAMP WHERE id = :id";
    $update_stmt = $db->prepare($update_query);
    $update_stmt->bindParam(':quantity', $quantity, PDO::PARAM_INT);
    $update_stmt->bindParam(':id', $cart_item_id, PDO::PARAM_INT);
    
    if ($update_stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Cart item updated successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update cart item']);
    }
}

function removeFromCart($db, $input) {
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'message' => 'User not logged in']);
        return;
    }
    
    $user_id = $_SESSION['user_id'];
    $product_id = $input['product_id'] ?? 0;
    
    if (!$product_id) {
        echo json_encode(['success' => false, 'message' => 'Product ID is required']);
        return;
    }
    
    $delete_query = "DELETE FROM cart_items WHERE user_id = :user_id AND product_id = :product_id";
    $delete_stmt = $db->prepare($delete_query);
    $delete_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $delete_stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
    
    if ($delete_stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Product removed from cart']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to remove product from cart']);
    }
}

function clearCart($db) {
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'message' => 'User not logged in']);
        return;
    }
    
    $user_id = $_SESSION['user_id'];
    
    $delete_query = "DELETE FROM cart_items WHERE user_id = :user_id";
    $delete_stmt = $db->prepare($delete_query);
    $delete_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    
    if ($delete_stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Cart cleared successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to clear cart']);
    }
}
?>
