<?php
/**
 * Search Suggestions API Endpoint
 * Provides real-time search suggestions
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $query = $_GET['q'] ?? '';
    $limit = intval($_GET['limit'] ?? 10);
    
    if (strlen($query) < 2) {
        echo json_encode(['success' => true, 'suggestions' => []]);
        exit;
    }
    
    $suggestions = [];
    
    // Get product suggestions
    $product_query = "
        SELECT DISTINCT name as text, 'product' as type, id
        FROM products 
        WHERE name LIKE :query AND status = 'active'
        ORDER BY total_sales DESC, name ASC
        LIMIT :limit
    ";
    
    $stmt = $db->prepare($product_query);
    $stmt->bindValue(':query', '%' . $query . '%');
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    
    $products = $stmt->fetchAll();
    foreach ($products as $product) {
        $suggestions[] = [
            'text' => $product['text'],
            'type' => 'product',
            'id' => $product['id']
        ];
    }
    
    // Get category suggestions
    if (count($suggestions) < $limit) {
        $remaining = $limit - count($suggestions);
        $category_query = "
            SELECT DISTINCT name as text, 'category' as type, slug as id
            FROM categories 
            WHERE name LIKE :query
            ORDER BY name ASC
            LIMIT :limit
        ";
        
        $stmt = $db->prepare($category_query);
        $stmt->bindValue(':query', '%' . $query . '%');
        $stmt->bindValue(':limit', $remaining, PDO::PARAM_INT);
        $stmt->execute();
        
        $categories = $stmt->fetchAll();
        foreach ($categories as $category) {
            $suggestions[] = [
                'text' => $category['text'],
                'type' => 'category',
                'id' => $category['id']
            ];
        }
    }
    
    // Get vendor suggestions
    if (count($suggestions) < $limit) {
        $remaining = $limit - count($suggestions);
        $vendor_query = "
            SELECT DISTINCT vp.business_name as text, 'vendor' as type, u.id
            FROM vendor_profiles vp
            LEFT JOIN users u ON vp.user_id = u.id
            WHERE vp.business_name LIKE :query 
            AND u.status = 'active' 
            AND vp.verification_status = 'verified'
            ORDER BY vp.business_name ASC
            LIMIT :limit
        ";
        
        $stmt = $db->prepare($vendor_query);
        $stmt->bindValue(':query', '%' . $query . '%');
        $stmt->bindValue(':limit', $remaining, PDO::PARAM_INT);
        $stmt->execute();
        
        $vendors = $stmt->fetchAll();
        foreach ($vendors as $vendor) {
            $suggestions[] = [
                'text' => $vendor['text'],
                'type' => 'vendor',
                'id' => $vendor['id']
            ];
        }
    }
    
    echo json_encode([
        'success' => true,
        'suggestions' => $suggestions
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}
?>
