# Musanze Marketplace

A professional, interactive product price comparison and ordering platform for Musanze District, Rwanda. This platform connects customers with local vendors, enabling price comparison, product discovery, and streamlined ordering processes.

## 🌟 Features

### For Customers
- **Product Search & Comparison**: Advanced search with real-time price comparison across vendors
- **Interactive Shopping**: Add to cart, wishlist, and compare products
- **User Reviews & Ratings**: Read and write product reviews
- **Order Tracking**: Track orders from placement to delivery
- **Mobile Responsive**: Optimized for all devices

### For Vendors
- **Vendor Dashboard**: Manage products, prices, and inventory
- **Order Management**: Process and track customer orders
- **Analytics**: Sales performance and customer insights
- **Product Catalog**: Easy product listing and management

### For Administrators
- **User Management**: Manage customers and vendors
- **Platform Analytics**: Monitor platform performance
- **Content Moderation**: Review and approve vendor applications
- **System Settings**: Configure platform settings

## 🎨 Design Features

- **Professional Color Scheme**: Green (#2E7D32, #4CAF50), White, and Black
- **Modern UI/UX**: Clean, intuitive interface with smooth animations
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile
- **Accessibility**: WCAG compliant design principles

## 🛠️ Technology Stack

### Backend
- **PHP 8.1+**: Modern PHP with object-oriented programming
- **MySQL 8.0**: Robust relational database
- **RESTful APIs**: Clean API architecture
- **Session Management**: Secure user authentication

### Frontend
- **HTML5**: Semantic markup
- **CSS3**: Modern styling with Flexbox/Grid
- **JavaScript (ES6+)**: Interactive functionality
- **Font Awesome**: Professional icons
- **Google Fonts**: Clean typography

### Additional Tools
- **XAMPP**: Local development environment
- **Composer**: PHP dependency management (recommended)
- **Git**: Version control

## 📁 Project Structure

```
musanze-marketplace/
├── api/                    # API endpoints
│   ├── auth.php           # Authentication API
│   ├── products.php       # Products API
│   └── cart.php           # Shopping cart API
├── assets/                # Static assets
│   ├── css/
│   │   └── style.css      # Main stylesheet
│   ├── js/
│   │   └── main.js        # Main JavaScript
│   └── images/            # Images and media
├── config/                # Configuration files
│   └── database.php       # Database configuration
├── database/              # Database files
│   └── schema.sql         # Database schema
├── includes/              # Shared components
│   ├── header.php         # Site header
│   └── footer.php         # Site footer
├── uploads/               # User uploaded files
├── views/                 # Page templates
│   ├── auth/              # Authentication pages
│   ├── home.php           # Homepage
│   └── products.php       # Products listing
└── index.php              # Main entry point
```

## 🚀 Installation & Setup

### Prerequisites
- XAMPP (or similar LAMP/WAMP stack)
- PHP 8.1 or higher
- MySQL 8.0 or higher
- Modern web browser

### Step 1: Setup XAMPP
1. Download and install XAMPP from [https://www.apachefriends.org/](https://www.apachefriends.org/)
2. Start Apache and MySQL services
3. Open phpMyAdmin at `http://localhost/phpmyadmin`

### Step 2: Database Setup
1. Create a new database named `musanze_marketplace`
2. Import the database schema:
   ```sql
   -- Copy and paste the contents of database/schema.sql
   -- Or use phpMyAdmin import feature
   ```

### Step 3: Configuration
1. Update database credentials in `config/database.php`:
   ```php
   private $host = 'localhost';
   private $db_name = 'musanze_marketplace';
   private $username = 'root';
   private $password = ''; // Your MySQL password
   ```

### Step 4: File Permissions
1. Create `uploads/` directory if it doesn't exist
2. Set appropriate permissions for file uploads

### Step 5: Access the Platform
1. Open your browser and go to `http://localhost/ange Final/`
2. The homepage should load successfully

## 👥 Demo Accounts

The platform comes with pre-configured demo accounts:

### Customer Account
- **Email**: <EMAIL>
- **Password**: demo123

### Vendor Account
- **Email**: <EMAIL>
- **Password**: demo123

### Admin Account
- **Email**: <EMAIL>
- **Password**: admin123

## 🔧 Configuration

### Email Settings
Update email configuration in `config/database.php`:
```php
define('SMTP_HOST', 'your-smtp-host');
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-email-password');
```

### File Upload Settings
Adjust upload limits in `config/database.php`:
```php
define('MAX_FILE_SIZE', 5242880); // 5MB
define('UPLOAD_PATH', 'uploads/');
```

## 📱 API Documentation

### Authentication Endpoints
- `POST /api/auth.php` - User login/registration
- `GET /api/auth.php?action=status` - Check auth status
- `GET /api/auth.php?action=logout` - User logout

### Product Endpoints
- `GET /api/products.php` - List products with filters
- `POST /api/products.php` - Create/update products (vendors only)

### Cart Endpoints
- `GET /api/cart.php` - Get cart items
- `POST /api/cart.php` - Add/remove cart items

## 🎯 Key Objectives Addressed

1. ✅ **Price Comparison**: Real-time price comparison across vendors
2. ✅ **User-Friendly Interface**: Intuitive design for easy navigation
3. ✅ **Vendor Management**: Complete vendor dashboard and tools
4. ✅ **Centralized Platform**: Single platform for all marketplace needs
5. ✅ **Mobile Optimization**: Responsive design for all devices

## 🔒 Security Features

- Password hashing with PHP's `password_hash()`
- SQL injection prevention with prepared statements
- XSS protection with input sanitization
- CSRF protection for forms
- Session security best practices

## 🚀 Future Enhancements

- Payment gateway integration (Mobile Money, Bank transfers)
- Real-time notifications with WebSocket
- Advanced analytics dashboard
- Multi-language support (Kinyarwanda, French, English)
- Mobile app development
- AI-powered product recommendations

## 🤝 Contributing

This platform is designed to serve the Musanze District community. Contributions are welcome!

## 📞 Support

For technical support or questions about the platform:
- Email: <EMAIL>
- Phone: +250 788 123 456

## 📄 License

This project is developed for the Musanze District community and local vendors.

---

**Musanze Marketplace** - Connecting Communities, Empowering Local Business 🇷🇼
