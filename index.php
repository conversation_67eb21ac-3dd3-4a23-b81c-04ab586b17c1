<?php
/**
 * Musanze District Marketplace - Main Entry Point
 * Professional Product Price Comparison Platform
 */

session_start();
require_once 'config/database.php';

// Simple routing system
$request = $_SERVER['REQUEST_URI'];
$path = parse_url($request, PHP_URL_PATH);
$path = str_replace('/ange Final', '', $path);

// Remove query string
$path = strtok($path, '?');

// Default to home if empty
if ($path === '/' || $path === '') {
    $path = '/home';
}

// Route handling
switch ($path) {
    case '/home':
    case '/':
        include 'views/home.php';
        break;
    
    case '/products':
        include 'views/products.php';
        break;
    
    case '/product':
        include 'views/product-detail.php';
        break;
    
    case '/compare':
        include 'views/compare.php';
        break;
    
    case '/cart':
        include 'views/cart.php';
        break;
    
    case '/checkout':
        include 'views/checkout.php';
        break;
    
    case '/login':
        include 'views/auth/login.php';
        break;
    
    case '/register':
        include 'views/auth/register.php';
        break;
    
    case '/vendor-register':
        include 'views/auth/vendor-register.php';
        break;
    
    case '/dashboard':
        include 'views/dashboard/index.php';
        break;
    
    case '/vendor-dashboard':
        include 'views/vendor/dashboard.php';
        break;
    
    case '/admin':
        include 'views/admin/dashboard.php';
        break;
    
    case '/api/products':
        include 'api/products.php';
        break;
    
    case '/api/auth':
        include 'api/auth.php';
        break;
    
    case '/api/cart':
        include 'api/cart.php';
        break;
    
    case '/api/orders':
        include 'api/orders.php';
        break;
    
    default:
        http_response_code(404);
        include 'views/404.php';
        break;
}
?>
