# Musanze Marketplace - Apache Configuration

# Enable URL Rewriting
RewriteEngine On

# Security Headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# HTTPS Redirect (uncomment for production)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Remove trailing slashes
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} (.+)/$
RewriteRule ^ %1 [R=301,L]

# Main routing rules
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Home page
RewriteRule ^$ index.php [L]

# Authentication routes
RewriteRule ^login/?$ views/auth/login.php [L]
RewriteRule ^register/?$ views/auth/register.php [L]
RewriteRule ^vendor-register/?$ views/auth/vendor-register.php [L]
RewriteRule ^logout/?$ api/auth.php?action=logout [L]

# Dashboard routes
RewriteRule ^dashboard/?$ views/dashboard/index.php [L]
RewriteRule ^vendor-dashboard/?$ views/vendor/dashboard.php [L]

# Product routes
RewriteRule ^products/?$ views/products.php [L]
RewriteRule ^product/([0-9]+)/?$ views/product-detail.php?id=$1 [L]
RewriteRule ^product/?$ views/product-detail.php [L]

# Shopping routes
RewriteRule ^cart/?$ views/cart.php [L]
RewriteRule ^compare/?$ views/compare.php [L]
RewriteRule ^wishlist/?$ views/wishlist.php [L]
RewriteRule ^checkout/?$ views/checkout.php [L]

# Vendor routes
RewriteRule ^vendor/([0-9]+)/?$ views/vendor-profile.php?id=$1 [L]
RewriteRule ^vendors/?$ views/vendors.php [L]

# Category routes
RewriteRule ^category/([a-zA-Z0-9-]+)/?$ views/products.php?category=$1 [L]

# Static pages
RewriteRule ^about/?$ views/pages/about.php [L]
RewriteRule ^contact/?$ views/pages/contact.php [L]
RewriteRule ^terms/?$ views/pages/terms.php [L]
RewriteRule ^privacy/?$ views/pages/privacy.php [L]
RewriteRule ^faq/?$ views/pages/faq.php [L]
RewriteRule ^support/?$ views/pages/support.php [L]

# Setup route (only for localhost)
RewriteCond %{HTTP_HOST} ^localhost$ [OR]
RewriteCond %{HTTP_HOST} ^127\.0\.0\.1$
RewriteRule ^setup/?$ setup.php [L]

# API routes (pass through)
RewriteRule ^api/ - [L]

# Assets and uploads (pass through)
RewriteRule ^assets/ - [L]
RewriteRule ^uploads/ - [L]

# 404 for everything else
RewriteRule ^(.*)$ views/404.php [L]

# File Security
<Files "*.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "config/*">
    Order Deny,Allow
    Deny from all
</Files>

<Files "database/*">
    Order Deny,Allow
    Deny from all
</Files>

# Prevent access to sensitive files
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Cache static assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType font/woff "access plus 1 month"
    ExpiresByType font/woff2 "access plus 1 month"
</IfModule>

# Gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Error pages
ErrorDocument 404 /views/404.php
ErrorDocument 403 /views/404.php
ErrorDocument 500 /views/404.php

# PHP Settings
<IfModule mod_php.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_vars 3000
    php_flag display_errors Off
    php_flag log_errors On
</IfModule>
