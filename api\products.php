<?php
/**
 * Products API Endpoint
 * Handles product listing, search, and filtering
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetRequest($db);
            break;
        case 'POST':
            handlePostRequest($db);
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}

function handleGetRequest($db) {
    // Get parameters
    $search = $_GET['search'] ?? '';
    $category = $_GET['category'] ?? '';
    $vendor = $_GET['vendor'] ?? '';
    $min_price = $_GET['min_price'] ?? '';
    $max_price = $_GET['max_price'] ?? '';
    $min_rating = $_GET['min_rating'] ?? '';
    $in_stock = isset($_GET['in_stock']) ? (bool)$_GET['in_stock'] : false;
    $featured = isset($_GET['featured']) ? (bool)$_GET['featured'] : false;
    $sort = $_GET['sort'] ?? 'newest';
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = intval($_GET['limit'] ?? PRODUCTS_PER_PAGE);
    $offset = ($page - 1) * $limit;
    
    // Build WHERE clause
    $where_conditions = ['p.status = :status'];
    $params = ['status' => 'active'];
    
    // Search
    if (!empty($search)) {
        $where_conditions[] = '(p.name LIKE :search OR p.description LIKE :search OR p.short_description LIKE :search)';
        $params['search'] = '%' . $search . '%';
    }
    
    // Category filter
    if (!empty($category)) {
        $where_conditions[] = 'c.slug = :category';
        $params['category'] = $category;
    }
    
    // Vendor filter
    if (!empty($vendor)) {
        $where_conditions[] = 'p.vendor_id = :vendor';
        $params['vendor'] = $vendor;
    }
    
    // Price range
    if (!empty($min_price)) {
        $where_conditions[] = 'p.price >= :min_price';
        $params['min_price'] = $min_price;
    }
    
    if (!empty($max_price)) {
        $where_conditions[] = 'p.price <= :max_price';
        $params['max_price'] = $max_price;
    }
    
    // Rating filter
    if (!empty($min_rating)) {
        $where_conditions[] = 'p.rating >= :min_rating';
        $params['min_rating'] = $min_rating;
    }
    
    // Stock filter
    if ($in_stock) {
        $where_conditions[] = 'p.stock_quantity > 0';
    }
    
    // Featured filter
    if ($featured) {
        $where_conditions[] = 'p.featured = 1';
    }
    
    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
    
    // Build ORDER BY clause
    $order_by = getOrderByClause($sort);
    
    // Count total products
    $count_query = "
        SELECT COUNT(*) as total
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN users v ON p.vendor_id = v.id
        LEFT JOIN vendor_profiles vp ON v.id = vp.user_id
        $where_clause
    ";
    
    $count_stmt = $db->prepare($count_query);
    $count_stmt->execute($params);
    $total_products = $count_stmt->fetch()['total'];
    
    // Get products
    $query = "
        SELECT 
            p.*,
            c.name as category_name,
            c.slug as category_slug,
            v.first_name as vendor_first_name,
            v.last_name as vendor_last_name,
            vp.business_name as vendor_business_name,
            (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN users v ON p.vendor_id = v.id
        LEFT JOIN vendor_profiles vp ON v.id = vp.user_id
        $where_clause
        $order_by
        LIMIT :limit OFFSET :offset
    ";
    
    $stmt = $db->prepare($query);
    
    // Bind parameters
    foreach ($params as $key => $value) {
        $stmt->bindValue(':' . $key, $value);
    }
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    
    $stmt->execute();
    $products = $stmt->fetchAll();
    
    // Format products
    $formatted_products = array_map('formatProduct', $products);
    
    // Calculate pagination
    $total_pages = ceil($total_products / $limit);
    
    $pagination = [
        'current_page' => $page,
        'total_pages' => $total_pages,
        'total' => $total_products,
        'per_page' => $limit,
        'has_next' => $page < $total_pages,
        'has_prev' => $page > 1
    ];
    
    echo json_encode([
        'success' => true,
        'products' => $formatted_products,
        'pagination' => $pagination
    ]);
}

function handlePostRequest($db) {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'get_product':
            getProductById($db, $input['product_id']);
            break;
        case 'update_stock':
            updateProductStock($db, $input);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
}

function getProductById($db, $product_id) {
    $query = "
        SELECT 
            p.*,
            c.name as category_name,
            c.slug as category_slug,
            v.first_name as vendor_first_name,
            v.last_name as vendor_last_name,
            vp.business_name as vendor_business_name,
            vp.business_description as vendor_description,
            vp.rating as vendor_rating
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN users v ON p.vendor_id = v.id
        LEFT JOIN vendor_profiles vp ON v.id = vp.user_id
        WHERE p.id = :product_id AND p.status = 'active'
    ";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
    $stmt->execute();
    
    $product = $stmt->fetch();
    
    if (!$product) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Product not found']);
        return;
    }
    
    // Get product images
    $images_query = "SELECT * FROM product_images WHERE product_id = :product_id ORDER BY is_primary DESC, sort_order ASC";
    $images_stmt = $db->prepare($images_query);
    $images_stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
    $images_stmt->execute();
    $images = $images_stmt->fetchAll();
    
    // Get product reviews
    $reviews_query = "
        SELECT 
            pr.*,
            u.first_name,
            u.last_name
        FROM product_reviews pr
        LEFT JOIN users u ON pr.user_id = u.id
        WHERE pr.product_id = :product_id AND pr.status = 'approved'
        ORDER BY pr.created_at DESC
        LIMIT 10
    ";
    $reviews_stmt = $db->prepare($reviews_query);
    $reviews_stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
    $reviews_stmt->execute();
    $reviews = $reviews_stmt->fetchAll();
    
    $formatted_product = formatProduct($product);
    $formatted_product['images'] = $images;
    $formatted_product['reviews'] = $reviews;
    
    echo json_encode([
        'success' => true,
        'product' => $formatted_product
    ]);
}

function formatProduct($product) {
    return [
        'id' => (int)$product['id'],
        'name' => $product['name'],
        'slug' => $product['slug'],
        'description' => $product['description'],
        'short_description' => $product['short_description'],
        'sku' => $product['sku'],
        'price' => (float)$product['price'],
        'compare_price' => $product['compare_price'] ? (float)$product['compare_price'] : null,
        'stock_quantity' => (int)$product['stock_quantity'],
        'weight' => $product['weight'] ? (float)$product['weight'] : null,
        'dimensions' => $product['dimensions'],
        'status' => $product['status'],
        'featured' => (bool)$product['featured'],
        'rating' => (float)$product['rating'],
        'total_reviews' => (int)$product['total_reviews'],
        'total_sales' => (int)$product['total_sales'],
        'category_name' => $product['category_name'],
        'category_slug' => $product['category_slug'],
        'vendor_name' => $product['vendor_business_name'] ?: 
                        ($product['vendor_first_name'] . ' ' . $product['vendor_last_name']),
        'vendor_business_name' => $product['vendor_business_name'],
        'image' => $product['primary_image'] ? BASE_URL . 'uploads/' . $product['primary_image'] : null,
        'created_at' => $product['created_at'],
        'updated_at' => $product['updated_at']
    ];
}

function getOrderByClause($sort) {
    switch ($sort) {
        case 'price_low':
            return 'ORDER BY p.price ASC';
        case 'price_high':
            return 'ORDER BY p.price DESC';
        case 'rating':
            return 'ORDER BY p.rating DESC, p.total_reviews DESC';
        case 'popular':
            return 'ORDER BY p.total_sales DESC, p.rating DESC';
        case 'name':
            return 'ORDER BY p.name ASC';
        case 'newest':
        default:
            return 'ORDER BY p.created_at DESC';
    }
}

function updateProductStock($db, $input) {
    session_start();
    
    // Check if user is logged in and is a vendor
    if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'vendor') {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Unauthorized']);
        return;
    }
    
    $product_id = $input['product_id'];
    $new_stock = $input['stock_quantity'];
    $vendor_id = $_SESSION['user_id'];
    
    // Verify product belongs to vendor
    $check_query = "SELECT id FROM products WHERE id = :product_id AND vendor_id = :vendor_id";
    $check_stmt = $db->prepare($check_query);
    $check_stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
    $check_stmt->bindParam(':vendor_id', $vendor_id, PDO::PARAM_INT);
    $check_stmt->execute();
    
    if (!$check_stmt->fetch()) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Product not found or access denied']);
        return;
    }
    
    // Update stock
    $update_query = "UPDATE products SET stock_quantity = :stock_quantity WHERE id = :product_id";
    $update_stmt = $db->prepare($update_query);
    $update_stmt->bindParam(':stock_quantity', $new_stock, PDO::PARAM_INT);
    $update_stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
    
    if ($update_stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Stock updated successfully']);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Failed to update stock']);
    }
}
?>
