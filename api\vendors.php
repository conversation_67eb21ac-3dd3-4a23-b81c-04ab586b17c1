<?php
/**
 * Vendors API Endpoint
 * Handles vendor listing and information
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetRequest($db);
            break;
        case 'POST':
            handlePostRequest($db);
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}

function handleGetRequest($db) {
    $action = $_GET['action'] ?? 'list';
    
    switch ($action) {
        case 'list':
            getVendorsList($db);
            break;
        case 'profile':
            getVendorProfile($db, $_GET['vendor_id'] ?? 0);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
}

function handlePostRequest($db) {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'update_profile':
            updateVendorProfile($db, $input);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
}

function getVendorsList($db) {
    $search = $_GET['search'] ?? '';
    $limit = intval($_GET['limit'] ?? 50);
    $offset = intval($_GET['offset'] ?? 0);
    
    // Build WHERE clause
    $where_conditions = ['u.user_type = :user_type', 'u.status = :status', 'vp.verification_status = :verification_status'];
    $params = [
        'user_type' => 'vendor',
        'status' => 'active',
        'verification_status' => 'verified'
    ];
    
    if (!empty($search)) {
        $where_conditions[] = '(vp.business_name LIKE :search OR u.first_name LIKE :search OR u.last_name LIKE :search)';
        $params['search'] = '%' . $search . '%';
    }
    
    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
    
    $query = "
        SELECT 
            u.id,
            u.first_name,
            u.last_name,
            u.email,
            u.phone,
            vp.business_name,
            vp.business_description,
            vp.business_address,
            vp.business_phone,
            vp.business_email,
            vp.rating,
            vp.total_reviews,
            vp.created_at,
            (SELECT COUNT(*) FROM products WHERE vendor_id = u.id AND status = 'active') as total_products
        FROM users u
        LEFT JOIN vendor_profiles vp ON u.id = vp.user_id
        $where_clause
        ORDER BY vp.rating DESC, vp.total_reviews DESC
        LIMIT :limit OFFSET :offset
    ";
    
    $stmt = $db->prepare($query);
    
    // Bind parameters
    foreach ($params as $key => $value) {
        $stmt->bindValue(':' . $key, $value);
    }
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    
    $stmt->execute();
    $vendors = $stmt->fetchAll();
    
    // Format vendors
    $formatted_vendors = array_map('formatVendor', $vendors);
    
    echo json_encode([
        'success' => true,
        'vendors' => $formatted_vendors
    ]);
}

function getVendorProfile($db, $vendor_id) {
    if (!$vendor_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Vendor ID is required']);
        return;
    }
    
    $query = "
        SELECT 
            u.*,
            vp.*
        FROM users u
        LEFT JOIN vendor_profiles vp ON u.id = vp.user_id
        WHERE u.id = :vendor_id AND u.user_type = 'vendor' AND u.status = 'active'
    ";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':vendor_id', $vendor_id, PDO::PARAM_INT);
    $stmt->execute();
    
    $vendor = $stmt->fetch();
    
    if (!$vendor) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Vendor not found']);
        return;
    }
    
    // Get vendor's products
    $products_query = "
        SELECT 
            p.*,
            c.name as category_name,
            (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.vendor_id = :vendor_id AND p.status = 'active'
        ORDER BY p.featured DESC, p.created_at DESC
        LIMIT 12
    ";
    
    $products_stmt = $db->prepare($products_query);
    $products_stmt->bindParam(':vendor_id', $vendor_id, PDO::PARAM_INT);
    $products_stmt->execute();
    $products = $products_stmt->fetchAll();
    
    // Get vendor reviews (from product reviews)
    $reviews_query = "
        SELECT 
            pr.rating,
            pr.title,
            pr.review_text,
            pr.created_at,
            u.first_name,
            u.last_name,
            p.name as product_name
        FROM product_reviews pr
        LEFT JOIN users u ON pr.user_id = u.id
        LEFT JOIN products p ON pr.product_id = p.id
        WHERE p.vendor_id = :vendor_id AND pr.status = 'approved'
        ORDER BY pr.created_at DESC
        LIMIT 10
    ";
    
    $reviews_stmt = $db->prepare($reviews_query);
    $reviews_stmt->bindParam(':vendor_id', $vendor_id, PDO::PARAM_INT);
    $reviews_stmt->execute();
    $reviews = $reviews_stmt->fetchAll();
    
    $formatted_vendor = formatVendor($vendor);
    $formatted_vendor['products'] = array_map('formatProduct', $products);
    $formatted_vendor['reviews'] = $reviews;
    
    echo json_encode([
        'success' => true,
        'vendor' => $formatted_vendor
    ]);
}

function updateVendorProfile($db, $input) {
    session_start();
    
    // Check if user is logged in and is a vendor
    if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'vendor') {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Unauthorized']);
        return;
    }
    
    $user_id = $_SESSION['user_id'];
    
    // Update user table
    $user_fields = ['first_name', 'last_name', 'email', 'phone'];
    $user_updates = [];
    $user_params = ['user_id' => $user_id];
    
    foreach ($user_fields as $field) {
        if (isset($input[$field])) {
            $user_updates[] = "$field = :$field";
            $user_params[$field] = $input[$field];
        }
    }
    
    if (!empty($user_updates)) {
        $user_query = "UPDATE users SET " . implode(', ', $user_updates) . " WHERE id = :user_id";
        $user_stmt = $db->prepare($user_query);
        $user_stmt->execute($user_params);
    }
    
    // Update vendor profile table
    $vendor_fields = ['business_name', 'business_description', 'business_address', 'business_phone', 'business_email', 'tax_number'];
    $vendor_updates = [];
    $vendor_params = ['user_id' => $user_id];
    
    foreach ($vendor_fields as $field) {
        if (isset($input[$field])) {
            $vendor_updates[] = "$field = :$field";
            $vendor_params[$field] = $input[$field];
        }
    }
    
    if (!empty($vendor_updates)) {
        $vendor_query = "UPDATE vendor_profiles SET " . implode(', ', $vendor_updates) . " WHERE user_id = :user_id";
        $vendor_stmt = $db->prepare($vendor_query);
        $vendor_stmt->execute($vendor_params);
    }
    
    echo json_encode(['success' => true, 'message' => 'Profile updated successfully']);
}

function formatVendor($vendor) {
    return [
        'id' => (int)$vendor['id'],
        'first_name' => $vendor['first_name'],
        'last_name' => $vendor['last_name'],
        'email' => $vendor['email'],
        'phone' => $vendor['phone'],
        'business_name' => $vendor['business_name'],
        'business_description' => $vendor['business_description'],
        'business_address' => $vendor['business_address'],
        'business_phone' => $vendor['business_phone'],
        'business_email' => $vendor['business_email'],
        'rating' => (float)($vendor['rating'] ?? 0),
        'total_reviews' => (int)($vendor['total_reviews'] ?? 0),
        'total_products' => (int)($vendor['total_products'] ?? 0),
        'created_at' => $vendor['created_at']
    ];
}

function formatProduct($product) {
    return [
        'id' => (int)$product['id'],
        'name' => $product['name'],
        'slug' => $product['slug'],
        'price' => (float)$product['price'],
        'compare_price' => $product['compare_price'] ? (float)$product['compare_price'] : null,
        'stock_quantity' => (int)$product['stock_quantity'],
        'status' => $product['status'],
        'featured' => (bool)$product['featured'],
        'rating' => (float)$product['rating'],
        'total_reviews' => (int)$product['total_reviews'],
        'category_name' => $product['category_name'],
        'image' => $product['primary_image'] ? BASE_URL . 'uploads/' . $product['primary_image'] : null,
        'created_at' => $product['created_at']
    ];
}
?>
