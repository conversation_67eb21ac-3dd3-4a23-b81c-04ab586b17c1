<?php
$current_page = 'home';
$page_title = 'Home';
$page_description = 'Professional Product Price Comparison Platform for Musanze District - Find the best deals from local vendors';

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero" style="background: linear-gradient(135deg, var(--primary-green), var(--secondary-green)); color: var(--white); padding: 4rem 0;">
    <div class="container">
        <div class="row align-center">
            <div class="col col-6">
                <h1 style="color: var(--white); font-size: 3rem; margin-bottom: 1.5rem;">
                    Find the Best Deals in <span style="color: #FFE082;">Musanze District</span>
                </h1>
                <p style="font-size: 1.2rem; margin-bottom: 2rem; color: #E8F5E8;">
                    Compare prices from local vendors, discover amazing products, and shop with confidence. 
                    Your one-stop marketplace for everything you need.
                </p>
                <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                    <a href="<?php echo BASE_URL; ?>products" class="btn btn-lg" style="background: var(--white); color: var(--primary-green);">
                        <i class="fas fa-search"></i> Browse Products
                    </a>
                    <a href="<?php echo BASE_URL; ?>vendor-register" class="btn btn-lg btn-secondary">
                        <i class="fas fa-store"></i> Become a Vendor
                    </a>
                </div>
            </div>
            <div class="col col-6 text-center">
                <div style="background: rgba(255,255,255,0.1); padding: 2rem; border-radius: var(--border-radius); backdrop-filter: blur(10px);">
                    <i class="fas fa-shopping-cart" style="font-size: 4rem; color: #FFE082; margin-bottom: 1rem;"></i>
                    <h3 style="color: var(--white); margin-bottom: 1rem;">Smart Shopping Made Easy</h3>
                    <p style="color: #E8F5E8;">Compare prices, read reviews, and make informed decisions</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features" style="padding: 4rem 0; background: var(--gray-light);">
    <div class="container">
        <div class="text-center mb-4">
            <h2>Why Choose Musanze Marketplace?</h2>
            <p style="font-size: 1.1rem; color: var(--gray-dark);">
                Discover the benefits of shopping with us
            </p>
        </div>
        
        <div class="row">
            <div class="col col-4">
                <div class="card text-center" style="height: 100%;">
                    <div class="card-body">
                        <div style="background: var(--light-green); width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem;">
                            <i class="fas fa-balance-scale" style="font-size: 2rem; color: var(--primary-green);"></i>
                        </div>
                        <h4>Price Comparison</h4>
                        <p>Compare prices from multiple vendors instantly and find the best deals in Musanze District.</p>
                    </div>
                </div>
            </div>
            
            <div class="col col-4">
                <div class="card text-center" style="height: 100%;">
                    <div class="card-body">
                        <div style="background: var(--light-green); width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem;">
                            <i class="fas fa-shield-alt" style="font-size: 2rem; color: var(--primary-green);"></i>
                        </div>
                        <h4>Verified Vendors</h4>
                        <p>Shop with confidence from verified local vendors who meet our quality standards.</p>
                    </div>
                </div>
            </div>
            
            <div class="col col-4">
                <div class="card text-center" style="height: 100%;">
                    <div class="card-body">
                        <div style="background: var(--light-green); width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem;">
                            <i class="fas fa-truck" style="font-size: 2rem; color: var(--primary-green);"></i>
                        </div>
                        <h4>Fast Delivery</h4>
                        <p>Quick and reliable delivery service throughout Musanze District and surrounding areas.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Products Section -->
<section class="featured-products" style="padding: 4rem 0;">
    <div class="container">
        <div class="text-center mb-4">
            <h2>Featured Products</h2>
            <p style="font-size: 1.1rem; color: var(--gray-dark);">
                Discover our most popular and trending products
            </p>
        </div>
        
        <div class="row" id="featured-products">
            <!-- Products will be loaded via JavaScript -->
            <div class="col col-12 text-center">
                <div style="padding: 2rem;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: var(--primary-green);"></i>
                    <p style="margin-top: 1rem;">Loading featured products...</p>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <a href="<?php echo BASE_URL; ?>products" class="btn btn-primary btn-lg">
                <i class="fas fa-th-large"></i> View All Products
            </a>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="categories" style="padding: 4rem 0; background: var(--gray-light);">
    <div class="container">
        <div class="text-center mb-4">
            <h2>Shop by Category</h2>
            <p style="font-size: 1.1rem; color: var(--gray-dark);">
                Find exactly what you're looking for
            </p>
        </div>
        
        <div class="row">
            <div class="col col-3">
                <a href="<?php echo BASE_URL; ?>products?category=electronics" class="card" style="text-decoration: none; display: block; transition: var(--transition);">
                    <div class="card-body text-center">
                        <i class="fas fa-laptop" style="font-size: 3rem; color: var(--primary-green); margin-bottom: 1rem;"></i>
                        <h5>Electronics</h5>
                        <p style="color: var(--gray-dark);">Phones, Laptops, Accessories</p>
                    </div>
                </a>
            </div>
            
            <div class="col col-3">
                <a href="<?php echo BASE_URL; ?>products?category=clothing-fashion" class="card" style="text-decoration: none; display: block; transition: var(--transition);">
                    <div class="card-body text-center">
                        <i class="fas fa-tshirt" style="font-size: 3rem; color: var(--primary-green); margin-bottom: 1rem;"></i>
                        <h5>Fashion</h5>
                        <p style="color: var(--gray-dark);">Clothing, Shoes, Accessories</p>
                    </div>
                </a>
            </div>
            
            <div class="col col-3">
                <a href="<?php echo BASE_URL; ?>products?category=home-garden" class="card" style="text-decoration: none; display: block; transition: var(--transition);">
                    <div class="card-body text-center">
                        <i class="fas fa-home" style="font-size: 3rem; color: var(--primary-green); margin-bottom: 1rem;"></i>
                        <h5>Home & Garden</h5>
                        <p style="color: var(--gray-dark);">Furniture, Decor, Tools</p>
                    </div>
                </a>
            </div>
            
            <div class="col col-3">
                <a href="<?php echo BASE_URL; ?>products?category=food-beverages" class="card" style="text-decoration: none; display: block; transition: var(--transition);">
                    <div class="card-body text-center">
                        <i class="fas fa-utensils" style="font-size: 3rem; color: var(--primary-green); margin-bottom: 1rem;"></i>
                        <h5>Food & Drinks</h5>
                        <p style="color: var(--gray-dark);">Fresh Food, Beverages</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="statistics" style="padding: 4rem 0; background: var(--primary-green); color: var(--white);">
    <div class="container">
        <div class="row text-center">
            <div class="col col-3">
                <div style="margin-bottom: 1rem;">
                    <i class="fas fa-users" style="font-size: 3rem; color: #FFE082;"></i>
                </div>
                <h3 style="color: var(--white); font-size: 2.5rem; margin-bottom: 0.5rem;">1,000+</h3>
                <p style="color: #E8F5E8;">Happy Customers</p>
            </div>
            
            <div class="col col-3">
                <div style="margin-bottom: 1rem;">
                    <i class="fas fa-store" style="font-size: 3rem; color: #FFE082;"></i>
                </div>
                <h3 style="color: var(--white); font-size: 2.5rem; margin-bottom: 0.5rem;">150+</h3>
                <p style="color: #E8F5E8;">Verified Vendors</p>
            </div>
            
            <div class="col col-3">
                <div style="margin-bottom: 1rem;">
                    <i class="fas fa-box" style="font-size: 3rem; color: #FFE082;"></i>
                </div>
                <h3 style="color: var(--white); font-size: 2.5rem; margin-bottom: 0.5rem;">5,000+</h3>
                <p style="color: #E8F5E8;">Products Available</p>
            </div>
            
            <div class="col col-3">
                <div style="margin-bottom: 1rem;">
                    <i class="fas fa-shipping-fast" style="font-size: 3rem; color: #FFE082;"></i>
                </div>
                <h3 style="color: var(--white); font-size: 2.5rem; margin-bottom: 0.5rem;">2,500+</h3>
                <p style="color: #E8F5E8;">Orders Delivered</p>
            </div>
        </div>
    </div>
</section>

<!-- Newsletter Section -->
<section class="newsletter" style="padding: 4rem 0;">
    <div class="container">
        <div class="row align-center">
            <div class="col col-6">
                <h3>Stay Updated with Best Deals</h3>
                <p style="color: var(--gray-dark); margin-bottom: 2rem;">
                    Subscribe to our newsletter and never miss out on amazing offers and new products from Musanze vendors.
                </p>
            </div>
            <div class="col col-6">
                <form class="newsletter-form" style="display: flex; gap: 1rem;">
                    <input type="email" 
                           class="form-control" 
                           placeholder="Enter your email address" 
                           required
                           style="flex: 1;">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i> Subscribe
                    </button>
                </form>
            </div>
        </div>
    </div>
</section>

<script>
// Load featured products
document.addEventListener('DOMContentLoaded', function() {
    loadFeaturedProducts();
});

function loadFeaturedProducts() {
    fetch('<?php echo BASE_URL; ?>api/products.php?featured=true&limit=8')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayFeaturedProducts(data.products);
            } else {
                document.getElementById('featured-products').innerHTML = 
                    '<div class="col col-12 text-center"><p>No featured products available at the moment.</p></div>';
            }
        })
        .catch(error => {
            console.error('Error loading featured products:', error);
            document.getElementById('featured-products').innerHTML = 
                '<div class="col col-12 text-center"><p>Error loading products. Please try again later.</p></div>';
        });
}

function displayFeaturedProducts(products) {
    const container = document.getElementById('featured-products');
    container.innerHTML = '';
    
    products.forEach(product => {
        const productCard = createProductCard(product);
        container.appendChild(productCard);
    });
}

function createProductCard(product) {
    const col = document.createElement('div');
    col.className = 'col col-3';
    
    col.innerHTML = `
        <div class="card product-card">
            <div class="product-image">
                <img src="${product.image || '<?php echo BASE_URL; ?>assets/images/placeholder.jpg'}" 
                     alt="${product.name}">
                ${product.featured ? '<div class="product-badge">Featured</div>' : ''}
                <div class="product-actions">
                    <button class="action-btn" onclick="addToWishlist(${product.id})" title="Add to Wishlist">
                        <i class="fas fa-heart"></i>
                    </button>
                    <button class="action-btn" onclick="addToCompare(${product.id})" title="Compare">
                        <i class="fas fa-balance-scale"></i>
                    </button>
                </div>
            </div>
            <div class="product-info">
                <div class="product-vendor">${product.vendor_name}</div>
                <h5 class="product-title">${product.name}</h5>
                <div class="product-rating">
                    <div class="stars">${generateStars(product.rating)}</div>
                    <span>(${product.total_reviews})</span>
                </div>
                <div class="product-price">
                    <span class="current-price">RWF ${formatPrice(product.price)}</span>
                    ${product.compare_price ? `<span class="original-price">RWF ${formatPrice(product.compare_price)}</span>` : ''}
                </div>
                <button class="btn btn-primary w-100" onclick="addToCart(${product.id})">
                    <i class="fas fa-cart-plus"></i> Add to Cart
                </button>
            </div>
        </div>
    `;
    
    return col;
}

function generateStars(rating) {
    let stars = '';
    for (let i = 1; i <= 5; i++) {
        if (i <= rating) {
            stars += '<i class="fas fa-star"></i>';
        } else if (i - 0.5 <= rating) {
            stars += '<i class="fas fa-star-half-alt"></i>';
        } else {
            stars += '<i class="far fa-star"></i>';
        }
    }
    return stars;
}

function formatPrice(price) {
    return new Intl.NumberFormat('en-RW').format(price);
}

function addToCart(productId) {
    // Add to cart functionality
    console.log('Adding product to cart:', productId);
}

function addToWishlist(productId) {
    // Add to wishlist functionality
    console.log('Adding product to wishlist:', productId);
}

function addToCompare(productId) {
    // Add to compare functionality
    console.log('Adding product to compare:', productId);
}
</script>

<?php include 'includes/footer.php'; ?>
