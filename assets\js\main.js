/**
 * Musanze Marketplace - Main JavaScript
 * Professional Product Price Comparison Platform
 */

// Global variables
let cart = JSON.parse(localStorage.getItem('cart')) || [];
let wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];
let compareList = JSON.parse(localStorage.getItem('compare')) || [];

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    updateCartCount();
    updateWishlistCount();
    updateCompareCount();
});

// Initialize application
function initializeApp() {
    // Initialize search functionality
    initializeSearch();
    
    // Initialize cart functionality
    initializeCart();
    
    // Initialize notifications
    initializeNotifications();
    
    // Initialize mobile menu
    initializeMobileMenu();
    
    // Initialize tooltips
    initializeTooltips();
}

// Search functionality
function initializeSearch() {
    const searchForm = document.querySelector('.search-form');
    const searchInput = document.querySelector('.search-input');
    
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            performSearch(searchInput.value);
        });
    }
    
    // Live search suggestions
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (this.value.length >= 3) {
                    showSearchSuggestions(this.value);
                } else {
                    hideSearchSuggestions();
                }
            }, 300);
        });
    }
}

function performSearch(query) {
    if (query.trim()) {
        window.location.href = `${BASE_URL}products?search=${encodeURIComponent(query)}`;
    }
}

function showSearchSuggestions(query) {
    fetch(`${BASE_URL}api/search-suggestions.php?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.suggestions.length > 0) {
                displaySearchSuggestions(data.suggestions);
            }
        })
        .catch(error => console.error('Search suggestions error:', error));
}

function displaySearchSuggestions(suggestions) {
    let suggestionsHtml = '<div class="search-suggestions">';
    suggestions.forEach(suggestion => {
        suggestionsHtml += `
            <div class="suggestion-item" onclick="selectSuggestion('${suggestion.text}')">
                <i class="fas fa-search"></i>
                <span>${suggestion.text}</span>
            </div>
        `;
    });
    suggestionsHtml += '</div>';
    
    // Add suggestions to search bar
    const searchBar = document.querySelector('.search-bar');
    const existingSuggestions = searchBar.querySelector('.search-suggestions');
    if (existingSuggestions) {
        existingSuggestions.remove();
    }
    searchBar.insertAdjacentHTML('beforeend', suggestionsHtml);
}

function selectSuggestion(text) {
    document.querySelector('.search-input').value = text;
    hideSearchSuggestions();
    performSearch(text);
}

function hideSearchSuggestions() {
    const suggestions = document.querySelector('.search-suggestions');
    if (suggestions) {
        suggestions.remove();
    }
}

// Cart functionality
function initializeCart() {
    // Update cart display on page load
    updateCartDisplay();
}

function addToCart(productId, quantity = 1) {
    // Check if user is logged in for persistent cart
    if (isUserLoggedIn()) {
        addToCartServer(productId, quantity);
    } else {
        addToCartLocal(productId, quantity);
    }
}

function addToCartLocal(productId, quantity) {
    const existingItem = cart.find(item => item.productId === productId);
    
    if (existingItem) {
        existingItem.quantity += quantity;
    } else {
        cart.push({
            productId: productId,
            quantity: quantity,
            addedAt: new Date().toISOString()
        });
    }
    
    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartCount();
    showNotification('Product added to cart!', 'success');
}

function addToCartServer(productId, quantity) {
    fetch(`${BASE_URL}api/cart.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'add',
            product_id: productId,
            quantity: quantity
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateCartCount();
            showNotification('Product added to cart!', 'success');
        } else {
            showNotification(data.message || 'Error adding to cart', 'error');
        }
    })
    .catch(error => {
        console.error('Cart error:', error);
        showNotification('Error adding to cart', 'error');
    });
}

function removeFromCart(productId) {
    if (isUserLoggedIn()) {
        removeFromCartServer(productId);
    } else {
        removeFromCartLocal(productId);
    }
}

function removeFromCartLocal(productId) {
    cart = cart.filter(item => item.productId !== productId);
    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartCount();
    updateCartDisplay();
    showNotification('Product removed from cart', 'info');
}

function removeFromCartServer(productId) {
    fetch(`${BASE_URL}api/cart.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'remove',
            product_id: productId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateCartCount();
            updateCartDisplay();
            showNotification('Product removed from cart', 'info');
        } else {
            showNotification(data.message || 'Error removing from cart', 'error');
        }
    })
    .catch(error => {
        console.error('Cart error:', error);
        showNotification('Error removing from cart', 'error');
    });
}

function updateCartCount() {
    const cartCountElement = document.getElementById('cart-count');
    if (cartCountElement) {
        if (isUserLoggedIn()) {
            // Fetch from server
            fetch(`${BASE_URL}api/cart.php?action=count`)
                .then(response => response.json())
                .then(data => {
                    cartCountElement.textContent = data.count || 0;
                })
                .catch(error => {
                    console.error('Error fetching cart count:', error);
                });
        } else {
            // Use local storage
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            cartCountElement.textContent = totalItems;
        }
    }
}

function updateCartDisplay() {
    // Update cart page if we're on it
    const cartContainer = document.getElementById('cart-items');
    if (cartContainer) {
        loadCartItems();
    }
}

// Wishlist functionality
function addToWishlist(productId) {
    if (!isUserLoggedIn()) {
        showNotification('Please login to add items to wishlist', 'warning');
        return;
    }
    
    fetch(`${BASE_URL}api/wishlist.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'add',
            product_id: productId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateWishlistCount();
            showNotification('Product added to wishlist!', 'success');
        } else {
            showNotification(data.message || 'Error adding to wishlist', 'error');
        }
    })
    .catch(error => {
        console.error('Wishlist error:', error);
        showNotification('Error adding to wishlist', 'error');
    });
}

function removeFromWishlist(productId) {
    fetch(`${BASE_URL}api/wishlist.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'remove',
            product_id: productId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateWishlistCount();
            showNotification('Product removed from wishlist', 'info');
        } else {
            showNotification(data.message || 'Error removing from wishlist', 'error');
        }
    })
    .catch(error => {
        console.error('Wishlist error:', error);
        showNotification('Error removing from wishlist', 'error');
    });
}

function updateWishlistCount() {
    const wishlistCountElement = document.getElementById('wishlist-count');
    if (wishlistCountElement && isUserLoggedIn()) {
        fetch(`${BASE_URL}api/wishlist.php?action=count`)
            .then(response => response.json())
            .then(data => {
                wishlistCountElement.textContent = data.count || 0;
            })
            .catch(error => {
                console.error('Error fetching wishlist count:', error);
            });
    }
}

// Compare functionality
function addToCompare(productId) {
    if (compareList.length >= 4) {
        showNotification('You can compare maximum 4 products', 'warning');
        return;
    }
    
    if (compareList.includes(productId)) {
        showNotification('Product already in compare list', 'info');
        return;
    }
    
    compareList.push(productId);
    localStorage.setItem('compare', JSON.stringify(compareList));
    updateCompareCount();
    showNotification('Product added to compare!', 'success');
}

function removeFromCompare(productId) {
    compareList = compareList.filter(id => id !== productId);
    localStorage.setItem('compare', JSON.stringify(compareList));
    updateCompareCount();
    showNotification('Product removed from compare', 'info');
}

function updateCompareCount() {
    const compareCountElement = document.getElementById('compare-count');
    if (compareCountElement) {
        compareCountElement.textContent = compareList.length;
    }
}

// Notification system
function initializeNotifications() {
    // Create notification container if it doesn't exist
    if (!document.getElementById('notification-container')) {
        const container = document.createElement('div');
        container.id = 'notification-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
        `;
        document.body.appendChild(container);
    }
}

function showNotification(message, type = 'info', duration = 5000) {
    const container = document.getElementById('notification-container');
    const notification = document.createElement('div');
    
    const colors = {
        success: '#4CAF50',
        error: '#f44336',
        warning: '#ff9800',
        info: '#2196F3'
    };
    
    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };
    
    notification.style.cssText = `
        background: ${colors[type]};
        color: white;
        padding: 15px 20px;
        margin-bottom: 10px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        display: flex;
        align-items: center;
        gap: 10px;
        animation: slideIn 0.3s ease;
        cursor: pointer;
    `;
    
    notification.innerHTML = `
        <i class="${icons[type]}"></i>
        <span>${message}</span>
        <i class="fas fa-times" style="margin-left: auto; cursor: pointer;"></i>
    `;
    
    // Add click to close
    notification.addEventListener('click', () => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    });
    
    container.appendChild(notification);
    
    // Auto remove after duration
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }
    }, duration);
}

// Utility functions
function isUserLoggedIn() {
    // Check if user session exists
    return document.body.dataset.userLoggedIn === 'true' || 
           document.querySelector('[data-user-id]') !== null;
}

function formatPrice(price) {
    return new Intl.NumberFormat('en-RW', {
        style: 'currency',
        currency: 'RWF',
        minimumFractionDigits: 0
    }).format(price);
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-RW', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Mobile menu functionality
function initializeMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuToggle && mobileMenu) {
        mobileMenuToggle.addEventListener('click', function() {
            mobileMenu.classList.toggle('active');
        });
        
        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!mobileMenu.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                mobileMenu.classList.remove('active');
            }
        });
    }
}

// Tooltip functionality
function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function showTooltip(e) {
    const text = e.target.getAttribute('data-tooltip');
    const tooltip = document.createElement('div');
    
    tooltip.className = 'tooltip';
    tooltip.textContent = text;
    tooltip.style.cssText = `
        position: absolute;
        background: #333;
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 14px;
        z-index: 1000;
        pointer-events: none;
        white-space: nowrap;
    `;
    
    document.body.appendChild(tooltip);
    
    const rect = e.target.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
    
    e.target.tooltip = tooltip;
}

function hideTooltip(e) {
    if (e.target.tooltip) {
        e.target.tooltip.remove();
        e.target.tooltip = null;
    }
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .search-suggestions {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ddd;
        border-top: none;
        border-radius: 0 0 8px 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        z-index: 1000;
        max-height: 300px;
        overflow-y: auto;
    }
    
    .suggestion-item {
        padding: 12px 16px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 10px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .suggestion-item:hover {
        background: #f8f9fa;
    }
    
    .suggestion-item:last-child {
        border-bottom: none;
    }
`;
document.head.appendChild(style);
