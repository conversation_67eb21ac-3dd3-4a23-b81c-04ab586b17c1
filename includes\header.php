<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?>Musanze Marketplace</title>
    <meta name="description" content="<?php echo isset($page_description) ? $page_description : 'Professional Product Price Comparison Platform for Musanze District'; ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo BASE_URL; ?>assets/images/favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Additional CSS -->
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css): ?>
            <link rel="stylesheet" href="<?php echo BASE_URL . $css; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <!-- Header Top -->
        <div class="header-top">
            <div class="container">
                <div class="row align-center justify-between">
                    <div class="col">
                        <span><i class="fas fa-phone"></i> +250 788 123 456</span>
                        <span style="margin-left: 2rem;"><i class="fas fa-envelope"></i> <EMAIL></span>
                    </div>
                    <div class="col text-right">
                        <?php if (isset($_SESSION['user_id'])): ?>
                            <span>Welcome, <?php echo htmlspecialchars($_SESSION['first_name'] ?? $_SESSION['username']); ?>!</span>
                            <a href="<?php echo BASE_URL; ?>api/auth.php?action=logout" style="margin-left: 1rem; color: white;">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        <?php else: ?>
                            <a href="<?php echo BASE_URL; ?>login" style="color: white;">
                                <i class="fas fa-sign-in-alt"></i> Login
                            </a>
                            <a href="<?php echo BASE_URL; ?>register" style="margin-left: 1rem; color: white;">
                                <i class="fas fa-user-plus"></i> Register
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Header Main -->
        <div class="header-main">
            <div class="container">
                <div class="row align-center">
                    <!-- Logo -->
                    <div class="col">
                        <a href="<?php echo BASE_URL; ?>" class="logo">
                            <i class="fas fa-store" style="color: var(--secondary-green);"></i>
                            Musanze Marketplace
                        </a>
                    </div>
                    
                    <!-- Search Bar -->
                    <div class="search-bar">
                        <form action="<?php echo BASE_URL; ?>products" method="GET" class="search-form">
                            <input type="text" 
                                   name="search" 
                                   class="search-input" 
                                   placeholder="Search for products, vendors, or categories..."
                                   value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                            <button type="submit" class="search-btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>
                    
                    <!-- Header Actions -->
                    <div class="header-actions">
                        <!-- Compare -->
                        <a href="<?php echo BASE_URL; ?>compare" class="header-link">
                            <i class="fas fa-balance-scale"></i>
                            <span>Compare</span>
                            <span class="cart-count" id="compare-count">0</span>
                        </a>
                        
                        <!-- Wishlist -->
                        <?php if (isset($_SESSION['user_id'])): ?>
                            <a href="<?php echo BASE_URL; ?>wishlist" class="header-link">
                                <i class="fas fa-heart"></i>
                                <span>Wishlist</span>
                                <span class="cart-count" id="wishlist-count">0</span>
                            </a>
                        <?php endif; ?>
                        
                        <!-- Cart -->
                        <a href="<?php echo BASE_URL; ?>cart" class="header-link">
                            <i class="fas fa-shopping-cart"></i>
                            <span>Cart</span>
                            <span class="cart-count" id="cart-count">0</span>
                        </a>
                        
                        <!-- User Dashboard -->
                        <?php if (isset($_SESSION['user_id'])): ?>
                            <?php if ($_SESSION['user_type'] === 'vendor'): ?>
                                <a href="<?php echo BASE_URL; ?>vendor-dashboard" class="header-link">
                                    <i class="fas fa-store"></i>
                                    <span>My Store</span>
                                </a>
                            <?php elseif ($_SESSION['user_type'] === 'admin'): ?>
                                <a href="<?php echo BASE_URL; ?>admin" class="header-link">
                                    <i class="fas fa-cog"></i>
                                    <span>Admin</span>
                                </a>
                            <?php else: ?>
                                <a href="<?php echo BASE_URL; ?>dashboard" class="header-link">
                                    <i class="fas fa-user"></i>
                                    <span>Account</span>
                                </a>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Navigation -->
        <nav class="navbar">
            <div class="container">
                <ul class="nav-menu">
                    <li><a href="<?php echo BASE_URL; ?>" class="nav-link <?php echo ($current_page === 'home') ? 'active' : ''; ?>">
                        <i class="fas fa-home"></i> Home
                    </a></li>
                    <li><a href="<?php echo BASE_URL; ?>products" class="nav-link <?php echo ($current_page === 'products') ? 'active' : ''; ?>">
                        <i class="fas fa-th-large"></i> All Products
                    </a></li>
                    <li><a href="<?php echo BASE_URL; ?>products?category=electronics" class="nav-link">
                        <i class="fas fa-laptop"></i> Electronics
                    </a></li>
                    <li><a href="<?php echo BASE_URL; ?>products?category=clothing-fashion" class="nav-link">
                        <i class="fas fa-tshirt"></i> Fashion
                    </a></li>
                    <li><a href="<?php echo BASE_URL; ?>products?category=home-garden" class="nav-link">
                        <i class="fas fa-home"></i> Home & Garden
                    </a></li>
                    <li><a href="<?php echo BASE_URL; ?>products?category=food-beverages" class="nav-link">
                        <i class="fas fa-utensils"></i> Food & Drinks
                    </a></li>
                    <?php if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] === 'customer'): ?>
                        <li><a href="<?php echo BASE_URL; ?>vendor-register" class="nav-link">
                            <i class="fas fa-store-alt"></i> Become a Vendor
                        </a></li>
                    <?php endif; ?>
                </ul>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
