<?php
$current_page = 'compare';
$page_title = 'Compare Products';
$page_description = 'Compare products side by side to make the best purchasing decision';

include 'includes/header.php';
?>

<div class="container" style="padding: 2rem 0;">
    <!-- Breadcrumb -->
    <nav class="breadcrumb" style="margin-bottom: 2rem;">
        <a href="<?php echo BASE_URL; ?>">Home</a>
        <span style="margin: 0 0.5rem; color: var(--gray-dark);">/</span>
        <span>Compare Products</span>
    </nav>

    <!-- Compare Header -->
    <div class="compare-header mb-4">
        <div class="row align-center justify-between">
            <div class="col">
                <h2><i class="fas fa-balance-scale" style="color: var(--primary-green);"></i> Compare Products</h2>
                <p style="color: var(--gray-dark); margin: 0;">Compare up to 4 products side by side</p>
            </div>
            <div class="col text-right">
                <button class="btn btn-secondary" onclick="clearComparison()">
                    <i class="fas fa-trash"></i> Clear All
                </button>
            </div>
        </div>
    </div>

    <!-- Compare Table -->
    <div class="compare-container">
        <div id="compare-content">
            <!-- Loading indicator -->
            <div class="loading-indicator text-center" style="padding: 3rem;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: var(--primary-green);"></i>
                <p style="margin-top: 1rem;">Loading comparison...</p>
            </div>
        </div>
    </div>

    <!-- Add More Products -->
    <div class="add-products-section mt-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-plus"></i> Add Products to Compare</h5>
            </div>
            <div class="card-body">
                <div class="search-products">
                    <div class="row">
                        <div class="col col-8">
                            <input type="text" 
                                   class="form-control" 
                                   placeholder="Search products to add to comparison..."
                                   id="product-search-input">
                        </div>
                        <div class="col col-4">
                            <button class="btn btn-primary w-100" onclick="searchProducts()">
                                <i class="fas fa-search"></i> Search
                            </button>
                        </div>
                    </div>
                </div>
                
                <div id="search-results" class="mt-3">
                    <!-- Search results will appear here -->
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.compare-container {
    overflow-x: auto;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.compare-table {
    width: 100%;
    min-width: 800px;
    border-collapse: collapse;
}

.compare-table th,
.compare-table td {
    padding: 1rem;
    border: 1px solid var(--gray-light);
    text-align: center;
    vertical-align: top;
}

.compare-table th {
    background: var(--gray-light);
    font-weight: 600;
    color: var(--black);
}

.compare-table .feature-label {
    background: var(--light-green);
    font-weight: 500;
    text-align: left;
    color: var(--primary-green);
}

.product-column {
    min-width: 200px;
    position: relative;
}

.product-image {
    width: 120px;
    height: 120px;
    margin: 0 auto 1rem;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--black);
}

.product-vendor {
    color: var(--gray-dark);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.product-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-green);
    margin-bottom: 1rem;
}

.product-rating {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.stars {
    color: #FFC107;
}

.remove-product {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #f44336;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.remove-product:hover {
    background: #d32f2f;
}

.feature-value {
    padding: 0.5rem;
}

.feature-value.highlight {
    background: var(--light-green);
    color: var(--primary-green);
    font-weight: 600;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 1rem;
}

.empty-compare {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-compare i {
    font-size: 4rem;
    color: var(--gray-medium);
    margin-bottom: 1rem;
}

.search-result-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid var(--gray-light);
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    transition: var(--transition);
}

.search-result-item:hover {
    border-color: var(--primary-green);
    background: var(--light-green);
}

.search-result-image {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius);
    overflow: hidden;
    flex-shrink: 0;
}

.search-result-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.search-result-details {
    flex: 1;
}

.search-result-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.search-result-price {
    color: var(--primary-green);
    font-weight: 600;
}

.add-to-compare-btn {
    background: var(--primary-green);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.add-to-compare-btn:hover {
    background: var(--secondary-green);
}

.add-to-compare-btn:disabled {
    background: var(--gray-medium);
    cursor: not-allowed;
}

@media (max-width: 768px) {
    .compare-table {
        min-width: 600px;
    }
    
    .product-column {
        min-width: 150px;
    }
    
    .product-image {
        width: 80px;
        height: 80px;
    }
    
    .search-result-item {
        flex-direction: column;
        text-align: center;
    }
}
</style>

<script>
let compareList = JSON.parse(localStorage.getItem('compare')) || [];

document.addEventListener('DOMContentLoaded', function() {
    loadCompareProducts();
    
    // Add enter key support for search
    document.getElementById('product-search-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchProducts();
        }
    });
});

function loadCompareProducts() {
    if (compareList.length === 0) {
        showEmptyCompare();
        return;
    }
    
    // Fetch product details for comparison
    Promise.all(compareList.map(productId => fetchProductDetails(productId)))
        .then(products => {
            displayCompareTable(products.filter(p => p !== null));
        })
        .catch(error => {
            console.error('Error loading compare products:', error);
            showEmptyCompare();
        });
}

function fetchProductDetails(productId) {
    return fetch(`<?php echo BASE_URL; ?>api/products.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'get_product',
            product_id: productId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            return data.product;
        }
        return null;
    })
    .catch(error => {
        console.error('Error fetching product:', error);
        return null;
    });
}

function displayCompareTable(products) {
    if (products.length === 0) {
        showEmptyCompare();
        return;
    }
    
    let html = `
        <table class="compare-table">
            <thead>
                <tr>
                    <th class="feature-label">Product</th>
                    ${products.map(product => `
                        <th class="product-column">
                            <button class="remove-product" onclick="removeFromCompare(${product.id})" title="Remove from comparison">
                                <i class="fas fa-times"></i>
                            </button>
                            <div class="product-image">
                                <img src="${product.image || '<?php echo BASE_URL; ?>assets/images/placeholder.jpg'}" 
                                     alt="${product.name}">
                            </div>
                            <div class="product-title">${product.name}</div>
                            <div class="product-vendor">${product.vendor_name}</div>
                            <div class="product-price">RWF ${formatPrice(product.price)}</div>
                            <div class="product-rating">
                                <div class="stars">${generateStars(product.rating)}</div>
                                <span>(${product.total_reviews})</span>
                            </div>
                            <div class="action-buttons">
                                <button class="btn btn-primary btn-sm" onclick="addToCart(${product.id})">
                                    <i class="fas fa-cart-plus"></i> Add to Cart
                                </button>
                                <a href="<?php echo BASE_URL; ?>product?id=${product.id}" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-eye"></i> View Details
                                </a>
                            </div>
                        </th>
                    `).join('')}
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="feature-label">Price</td>
                    ${products.map(product => `
                        <td class="feature-value ${getBestPriceClass(products, product)}">
                            RWF ${formatPrice(product.price)}
                            ${product.compare_price ? `<br><small style="text-decoration: line-through; color: var(--gray-dark);">RWF ${formatPrice(product.compare_price)}</small>` : ''}
                        </td>
                    `).join('')}
                </tr>
                <tr>
                    <td class="feature-label">Rating</td>
                    ${products.map(product => `
                        <td class="feature-value ${getBestRatingClass(products, product)}">
                            <div class="stars">${generateStars(product.rating)}</div>
                            ${product.rating}/5 (${product.total_reviews} reviews)
                        </td>
                    `).join('')}
                </tr>
                <tr>
                    <td class="feature-label">Stock</td>
                    ${products.map(product => `
                        <td class="feature-value">
                            ${product.stock_quantity > 0 ? 
                                `<span style="color: var(--secondary-green);">In Stock (${product.stock_quantity})</span>` : 
                                `<span style="color: #f44336;">Out of Stock</span>`
                            }
                        </td>
                    `).join('')}
                </tr>
                <tr>
                    <td class="feature-label">SKU</td>
                    ${products.map(product => `
                        <td class="feature-value">${product.sku || 'N/A'}</td>
                    `).join('')}
                </tr>
                <tr>
                    <td class="feature-label">Weight</td>
                    ${products.map(product => `
                        <td class="feature-value">${product.weight ? product.weight + ' kg' : 'N/A'}</td>
                    `).join('')}
                </tr>
                <tr>
                    <td class="feature-label">Dimensions</td>
                    ${products.map(product => `
                        <td class="feature-value">${product.dimensions || 'N/A'}</td>
                    `).join('')}
                </tr>
                <tr>
                    <td class="feature-label">Total Sales</td>
                    ${products.map(product => `
                        <td class="feature-value">${product.total_sales} sold</td>
                    `).join('')}
                </tr>
            </tbody>
        </table>
    `;
    
    document.getElementById('compare-content').innerHTML = html;
}

function showEmptyCompare() {
    document.getElementById('compare-content').innerHTML = `
        <div class="empty-compare">
            <i class="fas fa-balance-scale"></i>
            <h4>No products to compare</h4>
            <p style="color: var(--gray-dark); margin-bottom: 2rem;">
                Add products to your comparison list to see them side by side.
            </p>
            <a href="<?php echo BASE_URL; ?>products" class="btn btn-primary">
                <i class="fas fa-search"></i> Browse Products
            </a>
        </div>
    `;
}

function removeFromCompare(productId) {
    compareList = compareList.filter(id => id !== productId);
    localStorage.setItem('compare', JSON.stringify(compareList));
    updateCompareCount();
    loadCompareProducts();
    showNotification('Product removed from comparison', 'info');
}

function clearComparison() {
    if (compareList.length === 0) {
        showNotification('Comparison list is already empty', 'info');
        return;
    }
    
    if (confirm('Are you sure you want to clear all products from comparison?')) {
        compareList = [];
        localStorage.setItem('compare', JSON.stringify(compareList));
        updateCompareCount();
        loadCompareProducts();
        showNotification('Comparison cleared', 'info');
    }
}

function searchProducts() {
    const query = document.getElementById('product-search-input').value.trim();
    
    if (!query) {
        showNotification('Please enter a search term', 'warning');
        return;
    }
    
    const resultsContainer = document.getElementById('search-results');
    resultsContainer.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Searching...</div>';
    
    fetch(`<?php echo BASE_URL; ?>api/products.php?search=${encodeURIComponent(query)}&limit=5`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.products.length > 0) {
                displaySearchResults(data.products);
            } else {
                resultsContainer.innerHTML = '<div class="text-center" style="color: var(--gray-dark);">No products found</div>';
            }
        })
        .catch(error => {
            console.error('Search error:', error);
            resultsContainer.innerHTML = '<div class="text-center" style="color: #f44336;">Error searching products</div>';
        });
}

function displaySearchResults(products) {
    const resultsContainer = document.getElementById('search-results');
    
    let html = '';
    products.forEach(product => {
        const isInCompare = compareList.includes(product.id);
        const canAdd = compareList.length < 4 && !isInCompare;
        
        html += `
            <div class="search-result-item">
                <div class="search-result-image">
                    <img src="${product.image || '<?php echo BASE_URL; ?>assets/images/placeholder.jpg'}" 
                         alt="${product.name}">
                </div>
                <div class="search-result-details">
                    <div class="search-result-title">${product.name}</div>
                    <div class="search-result-price">RWF ${formatPrice(product.price)}</div>
                </div>
                <button class="add-to-compare-btn" 
                        onclick="addToCompareFromSearch(${product.id})"
                        ${!canAdd ? 'disabled' : ''}>
                    ${isInCompare ? 'Already Added' : (compareList.length >= 4 ? 'Limit Reached' : 'Add to Compare')}
                </button>
            </div>
        `;
    });
    
    resultsContainer.innerHTML = html;
}

function addToCompareFromSearch(productId) {
    if (compareList.length >= 4) {
        showNotification('You can compare maximum 4 products', 'warning');
        return;
    }
    
    if (compareList.includes(productId)) {
        showNotification('Product already in comparison', 'info');
        return;
    }
    
    compareList.push(productId);
    localStorage.setItem('compare', JSON.stringify(compareList));
    updateCompareCount();
    loadCompareProducts();
    showNotification('Product added to comparison!', 'success');
    
    // Clear search results
    document.getElementById('search-results').innerHTML = '';
    document.getElementById('product-search-input').value = '';
}

function getBestPriceClass(products, currentProduct) {
    const prices = products.map(p => p.price);
    const minPrice = Math.min(...prices);
    return currentProduct.price === minPrice ? 'highlight' : '';
}

function getBestRatingClass(products, currentProduct) {
    const ratings = products.map(p => p.rating);
    const maxRating = Math.max(...ratings);
    return currentProduct.rating === maxRating ? 'highlight' : '';
}

function generateStars(rating) {
    let stars = '';
    for (let i = 1; i <= 5; i++) {
        if (i <= rating) {
            stars += '<i class="fas fa-star"></i>';
        } else if (i - 0.5 <= rating) {
            stars += '<i class="fas fa-star-half-alt"></i>';
        } else {
            stars += '<i class="far fa-star"></i>';
        }
    }
    return stars;
}

function formatPrice(price) {
    return new Intl.NumberFormat('en-RW').format(price);
}

function updateCompareCount() {
    const compareCountElement = document.getElementById('compare-count');
    if (compareCountElement) {
        compareCountElement.textContent = compareList.length;
    }
}

function addToCart(productId) {
    // This function should be defined in main.js
    if (typeof window.addToCart === 'function') {
        window.addToCart(productId);
    } else {
        showNotification('Add to cart functionality not available', 'error');
    }
}

// Show notification function
function showNotification(message, type = 'info', duration = 5000) {
    let container = document.getElementById('notification-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notification-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
        `;
        document.body.appendChild(container);
    }
    
    const notification = document.createElement('div');
    
    const colors = {
        success: '#4CAF50',
        error: '#f44336',
        warning: '#ff9800',
        info: '#2196F3'
    };
    
    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };
    
    notification.style.cssText = `
        background: ${colors[type]};
        color: white;
        padding: 15px 20px;
        margin-bottom: 10px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        display: flex;
        align-items: center;
        gap: 10px;
        animation: slideIn 0.3s ease;
        cursor: pointer;
    `;
    
    notification.innerHTML = `
        <i class="${icons[type]}"></i>
        <span>${message}</span>
        <i class="fas fa-times" style="margin-left: auto; cursor: pointer;"></i>
    `;
    
    notification.addEventListener('click', () => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    });
    
    container.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }
    }, duration);
}
</script>

<?php include 'includes/footer.php'; ?>
