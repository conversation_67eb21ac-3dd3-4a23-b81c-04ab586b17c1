<?php
$current_page = 'products';
$page_title = 'Products';
$page_description = 'Browse and compare products from verified vendors in Musanze District';

include 'includes/header.php';

// Get filter parameters
$search = $_GET['search'] ?? '';
$category = $_GET['category'] ?? '';
$min_price = $_GET['min_price'] ?? '';
$max_price = $_GET['max_price'] ?? '';
$sort = $_GET['sort'] ?? 'newest';
$page = max(1, intval($_GET['page'] ?? 1));
?>

<div class="container" style="padding: 2rem 0;">
    <!-- Breadcrumb -->
    <nav class="breadcrumb" style="margin-bottom: 2rem;">
        <a href="<?php echo BASE_URL; ?>">Home</a>
        <span style="margin: 0 0.5rem; color: var(--gray-dark);">/</span>
        <span>Products</span>
        <?php if ($search): ?>
            <span style="margin: 0 0.5rem; color: var(--gray-dark);">/</span>
            <span>Search: "<?php echo htmlspecialchars($search); ?>"</span>
        <?php endif; ?>
        <?php if ($category): ?>
            <span style="margin: 0 0.5rem; color: var(--gray-dark);">/</span>
            <span><?php echo ucfirst(str_replace('-', ' ', $category)); ?></span>
        <?php endif; ?>
    </nav>

    <div class="row">
        <!-- Sidebar Filters -->
        <div class="col col-3">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-filter"></i> Filters</h5>
                </div>
                <div class="card-body">
                    <form id="filter-form" method="GET">
                        <!-- Search -->
                        <div class="form-group">
                            <label class="form-label">Search Products</label>
                            <input type="text" 
                                   name="search" 
                                   class="form-control" 
                                   placeholder="Product name, brand..."
                                   value="<?php echo htmlspecialchars($search); ?>">
                        </div>

                        <!-- Categories -->
                        <div class="form-group">
                            <label class="form-label">Category</label>
                            <select name="category" class="form-control form-select">
                                <option value="">All Categories</option>
                                <option value="electronics" <?php echo $category === 'electronics' ? 'selected' : ''; ?>>Electronics</option>
                                <option value="clothing-fashion" <?php echo $category === 'clothing-fashion' ? 'selected' : ''; ?>>Clothing & Fashion</option>
                                <option value="home-garden" <?php echo $category === 'home-garden' ? 'selected' : ''; ?>>Home & Garden</option>
                                <option value="food-beverages" <?php echo $category === 'food-beverages' ? 'selected' : ''; ?>>Food & Beverages</option>
                                <option value="health-beauty" <?php echo $category === 'health-beauty' ? 'selected' : ''; ?>>Health & Beauty</option>
                                <option value="sports-outdoors" <?php echo $category === 'sports-outdoors' ? 'selected' : ''; ?>>Sports & Outdoors</option>
                                <option value="books-education" <?php echo $category === 'books-education' ? 'selected' : ''; ?>>Books & Education</option>
                                <option value="automotive" <?php echo $category === 'automotive' ? 'selected' : ''; ?>>Automotive</option>
                            </select>
                        </div>

                        <!-- Price Range -->
                        <div class="form-group">
                            <label class="form-label">Price Range (RWF)</label>
                            <div class="row">
                                <div class="col col-6">
                                    <input type="number" 
                                           name="min_price" 
                                           class="form-control" 
                                           placeholder="Min"
                                           value="<?php echo htmlspecialchars($min_price); ?>">
                                </div>
                                <div class="col col-6">
                                    <input type="number" 
                                           name="max_price" 
                                           class="form-control" 
                                           placeholder="Max"
                                           value="<?php echo htmlspecialchars($max_price); ?>">
                                </div>
                            </div>
                        </div>

                        <!-- Rating -->
                        <div class="form-group">
                            <label class="form-label">Minimum Rating</label>
                            <select name="min_rating" class="form-control form-select">
                                <option value="">Any Rating</option>
                                <option value="4">4+ Stars</option>
                                <option value="3">3+ Stars</option>
                                <option value="2">2+ Stars</option>
                                <option value="1">1+ Stars</option>
                            </select>
                        </div>

                        <!-- Vendor -->
                        <div class="form-group">
                            <label class="form-label">Vendor</label>
                            <select name="vendor" class="form-control form-select" id="vendor-select">
                                <option value="">All Vendors</option>
                                <!-- Vendors will be loaded via JavaScript -->
                            </select>
                        </div>

                        <!-- Availability -->
                        <div class="form-group">
                            <label class="form-label">
                                <input type="checkbox" name="in_stock" value="1" style="margin-right: 0.5rem;">
                                In Stock Only
                            </label>
                        </div>

                        <!-- Featured -->
                        <div class="form-group">
                            <label class="form-label">
                                <input type="checkbox" name="featured" value="1" style="margin-right: 0.5rem;">
                                Featured Products Only
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i> Apply Filters
                        </button>
                        
                        <a href="<?php echo BASE_URL; ?>products" class="btn btn-secondary w-100 mt-2">
                            <i class="fas fa-times"></i> Clear Filters
                        </a>
                    </form>
                </div>
            </div>

            <!-- Popular Categories -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6>Popular Categories</h6>
                </div>
                <div class="card-body">
                    <div class="category-links">
                        <a href="<?php echo BASE_URL; ?>products?category=electronics" class="category-link">
                            <i class="fas fa-laptop"></i> Electronics
                        </a>
                        <a href="<?php echo BASE_URL; ?>products?category=clothing-fashion" class="category-link">
                            <i class="fas fa-tshirt"></i> Fashion
                        </a>
                        <a href="<?php echo BASE_URL; ?>products?category=home-garden" class="category-link">
                            <i class="fas fa-home"></i> Home & Garden
                        </a>
                        <a href="<?php echo BASE_URL; ?>products?category=food-beverages" class="category-link">
                            <i class="fas fa-utensils"></i> Food & Drinks
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col col-9">
            <!-- Results Header -->
            <div class="results-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem; padding: 1rem; background: var(--gray-light); border-radius: var(--border-radius);">
                <div>
                    <h4 id="results-count">Loading products...</h4>
                    <?php if ($search): ?>
                        <p style="margin: 0; color: var(--gray-dark);">
                            Search results for: <strong>"<?php echo htmlspecialchars($search); ?>"</strong>
                        </p>
                    <?php endif; ?>
                </div>
                
                <div class="sort-options" style="display: flex; align-items: center; gap: 1rem;">
                    <label style="margin: 0;">Sort by:</label>
                    <select name="sort" class="form-control form-select" style="width: auto;" onchange="updateSort(this.value)">
                        <option value="newest" <?php echo $sort === 'newest' ? 'selected' : ''; ?>>Newest First</option>
                        <option value="price_low" <?php echo $sort === 'price_low' ? 'selected' : ''; ?>>Price: Low to High</option>
                        <option value="price_high" <?php echo $sort === 'price_high' ? 'selected' : ''; ?>>Price: High to Low</option>
                        <option value="rating" <?php echo $sort === 'rating' ? 'selected' : ''; ?>>Highest Rated</option>
                        <option value="popular" <?php echo $sort === 'popular' ? 'selected' : ''; ?>>Most Popular</option>
                        <option value="name" <?php echo $sort === 'name' ? 'selected' : ''; ?>>Name A-Z</option>
                    </select>
                    
                    <div class="view-toggle" style="display: flex; gap: 0.5rem;">
                        <button class="btn btn-sm view-btn active" data-view="grid" title="Grid View">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="btn btn-sm view-btn" data-view="list" title="List View">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Products Grid -->
            <div id="products-container" class="products-grid">
                <!-- Loading indicator -->
                <div class="loading-indicator text-center" style="grid-column: 1 / -1; padding: 3rem;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: var(--primary-green);"></i>
                    <p style="margin-top: 1rem;">Loading products...</p>
                </div>
            </div>

            <!-- Pagination -->
            <div id="pagination-container" class="pagination-container text-center mt-4">
                <!-- Pagination will be loaded via JavaScript -->
            </div>
        </div>
    </div>
</div>

<style>
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
}

.products-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.product-card-list {
    display: flex;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
}

.product-card-list:hover {
    box-shadow: var(--shadow-hover);
}

.product-card-list .product-image {
    width: 200px;
    height: 150px;
    flex-shrink: 0;
}

.product-card-list .product-info {
    flex: 1;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.category-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.category-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    text-decoration: none;
    color: var(--gray-dark);
}

.category-link:hover {
    background: var(--light-green);
    color: var(--primary-green);
}

.view-btn {
    background: var(--gray-light);
    border: 1px solid var(--gray-medium);
    color: var(--gray-dark);
}

.view-btn.active {
    background: var(--primary-green);
    color: var(--white);
    border-color: var(--primary-green);
}

.pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.pagination a,
.pagination span {
    padding: 0.5rem 1rem;
    border: 1px solid var(--gray-medium);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--gray-dark);
    transition: var(--transition);
}

.pagination a:hover {
    background: var(--primary-green);
    color: var(--white);
    border-color: var(--primary-green);
}

.pagination .current {
    background: var(--primary-green);
    color: var(--white);
    border-color: var(--primary-green);
}

@media (max-width: 768px) {
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }
    
    .results-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .sort-options {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .product-card-list {
        flex-direction: column;
    }
    
    .product-card-list .product-image {
        width: 100%;
        height: 200px;
    }
}
</style>

<script>
// Global variables
let currentView = 'grid';
let currentPage = <?php echo $page; ?>;
let currentFilters = {
    search: '<?php echo addslashes($search); ?>',
    category: '<?php echo addslashes($category); ?>',
    min_price: '<?php echo addslashes($min_price); ?>',
    max_price: '<?php echo addslashes($max_price); ?>',
    sort: '<?php echo addslashes($sort); ?>'
};

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadProducts();
    loadVendors();
    initializeViewToggle();
    initializeFilterForm();
});

// Load products
function loadProducts(page = 1) {
    const params = new URLSearchParams({
        page: page,
        ...currentFilters
    });
    
    fetch(`<?php echo BASE_URL; ?>api/products.php?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayProducts(data.products);
                displayPagination(data.pagination);
                updateResultsCount(data.pagination.total);
            } else {
                showError('Error loading products');
            }
        })
        .catch(error => {
            console.error('Error loading products:', error);
            showError('Error loading products');
        });
}

// Display products
function displayProducts(products) {
    const container = document.getElementById('products-container');
    container.innerHTML = '';
    
    if (products.length === 0) {
        container.innerHTML = `
            <div style="grid-column: 1 / -1; text-center; padding: 3rem;">
                <i class="fas fa-search" style="font-size: 3rem; color: var(--gray-medium); margin-bottom: 1rem;"></i>
                <h4>No products found</h4>
                <p style="color: var(--gray-dark);">Try adjusting your search criteria or browse our categories.</p>
                <a href="<?php echo BASE_URL; ?>products" class="btn btn-primary mt-2">
                    <i class="fas fa-refresh"></i> View All Products
                </a>
            </div>
        `;
        return;
    }
    
    products.forEach(product => {
        const productElement = createProductElement(product);
        container.appendChild(productElement);
    });
}

// Create product element
function createProductElement(product) {
    const element = document.createElement('div');
    
    if (currentView === 'grid') {
        element.innerHTML = `
            <div class="card product-card">
                <div class="product-image">
                    <img src="${product.image || '<?php echo BASE_URL; ?>assets/images/placeholder.jpg'}" 
                         alt="${product.name}">
                    ${product.featured ? '<div class="product-badge">Featured</div>' : ''}
                    <div class="product-actions">
                        <button class="action-btn" onclick="addToWishlist(${product.id})" title="Add to Wishlist">
                            <i class="fas fa-heart"></i>
                        </button>
                        <button class="action-btn" onclick="addToCompare(${product.id})" title="Compare">
                            <i class="fas fa-balance-scale"></i>
                        </button>
                        <button class="action-btn" onclick="quickView(${product.id})" title="Quick View">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                <div class="product-info">
                    <div class="product-vendor">${product.vendor_name}</div>
                    <h5 class="product-title">
                        <a href="<?php echo BASE_URL; ?>product?id=${product.id}" style="color: inherit; text-decoration: none;">
                            ${product.name}
                        </a>
                    </h5>
                    <div class="product-rating">
                        <div class="stars">${generateStars(product.rating)}</div>
                        <span>(${product.total_reviews})</span>
                    </div>
                    <div class="product-price">
                        <span class="current-price">RWF ${formatPrice(product.price)}</span>
                        ${product.compare_price ? `<span class="original-price">RWF ${formatPrice(product.compare_price)}</span>` : ''}
                    </div>
                    <button class="btn btn-primary w-100" onclick="addToCart(${product.id})">
                        <i class="fas fa-cart-plus"></i> Add to Cart
                    </button>
                </div>
            </div>
        `;
    } else {
        element.innerHTML = `
            <div class="product-card-list">
                <div class="product-image">
                    <img src="${product.image || '<?php echo BASE_URL; ?>assets/images/placeholder.jpg'}" 
                         alt="${product.name}">
                </div>
                <div class="product-info">
                    <div>
                        <div class="product-vendor">${product.vendor_name}</div>
                        <h5 class="product-title">
                            <a href="<?php echo BASE_URL; ?>product?id=${product.id}" style="color: inherit; text-decoration: none;">
                                ${product.name}
                            </a>
                        </h5>
                        <p style="color: var(--gray-dark); margin-bottom: 1rem;">${product.short_description || ''}</p>
                        <div class="product-rating">
                            <div class="stars">${generateStars(product.rating)}</div>
                            <span>(${product.total_reviews})</span>
                        </div>
                    </div>
                    <div>
                        <div class="product-price mb-2">
                            <span class="current-price">RWF ${formatPrice(product.price)}</span>
                            ${product.compare_price ? `<span class="original-price">RWF ${formatPrice(product.compare_price)}</span>` : ''}
                        </div>
                        <div style="display: flex; gap: 0.5rem;">
                            <button class="btn btn-primary" onclick="addToCart(${product.id})">
                                <i class="fas fa-cart-plus"></i> Add to Cart
                            </button>
                            <button class="btn btn-secondary" onclick="addToWishlist(${product.id})">
                                <i class="fas fa-heart"></i>
                            </button>
                            <button class="btn btn-secondary" onclick="addToCompare(${product.id})">
                                <i class="fas fa-balance-scale"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    return element;
}

// Initialize view toggle
function initializeViewToggle() {
    const viewButtons = document.querySelectorAll('.view-btn');
    
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const view = this.dataset.view;
            switchView(view);
            
            // Update active state
            viewButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
        });
    });
}

// Switch view
function switchView(view) {
    currentView = view;
    const container = document.getElementById('products-container');
    
    if (view === 'grid') {
        container.className = 'products-grid';
    } else {
        container.className = 'products-list';
    }
    
    // Reload products with new view
    loadProducts(currentPage);
}

// Update sort
function updateSort(sort) {
    currentFilters.sort = sort;
    currentPage = 1;
    loadProducts();
    updateURL();
}

// Initialize filter form
function initializeFilterForm() {
    const form = document.getElementById('filter-form');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(form);
        currentFilters = {};
        
        for (let [key, value] of formData.entries()) {
            if (value.trim()) {
                currentFilters[key] = value;
            }
        }
        
        currentPage = 1;
        loadProducts();
        updateURL();
    });
}

// Load vendors for filter
function loadVendors() {
    fetch('<?php echo BASE_URL; ?>api/vendors.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('vendor-select');
                data.vendors.forEach(vendor => {
                    const option = document.createElement('option');
                    option.value = vendor.id;
                    option.textContent = vendor.business_name;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => console.error('Error loading vendors:', error));
}

// Update results count
function updateResultsCount(total) {
    document.getElementById('results-count').textContent = 
        `${total} product${total !== 1 ? 's' : ''} found`;
}

// Display pagination
function displayPagination(pagination) {
    const container = document.getElementById('pagination-container');
    
    if (pagination.total_pages <= 1) {
        container.innerHTML = '';
        return;
    }
    
    let html = '<div class="pagination">';
    
    // Previous button
    if (pagination.current_page > 1) {
        html += `<a href="#" onclick="goToPage(${pagination.current_page - 1})">
            <i class="fas fa-chevron-left"></i> Previous
        </a>`;
    }
    
    // Page numbers
    for (let i = Math.max(1, pagination.current_page - 2); 
         i <= Math.min(pagination.total_pages, pagination.current_page + 2); 
         i++) {
        if (i === pagination.current_page) {
            html += `<span class="current">${i}</span>`;
        } else {
            html += `<a href="#" onclick="goToPage(${i})">${i}</a>`;
        }
    }
    
    // Next button
    if (pagination.current_page < pagination.total_pages) {
        html += `<a href="#" onclick="goToPage(${pagination.current_page + 1})">
            Next <i class="fas fa-chevron-right"></i>
        </a>`;
    }
    
    html += '</div>';
    container.innerHTML = html;
}

// Go to page
function goToPage(page) {
    currentPage = page;
    loadProducts(page);
    updateURL();
    
    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

// Update URL
function updateURL() {
    const params = new URLSearchParams(currentFilters);
    if (currentPage > 1) {
        params.set('page', currentPage);
    }
    
    const newURL = `${window.location.pathname}?${params.toString()}`;
    window.history.pushState({}, '', newURL);
}

// Utility functions
function generateStars(rating) {
    let stars = '';
    for (let i = 1; i <= 5; i++) {
        if (i <= rating) {
            stars += '<i class="fas fa-star"></i>';
        } else if (i - 0.5 <= rating) {
            stars += '<i class="fas fa-star-half-alt"></i>';
        } else {
            stars += '<i class="far fa-star"></i>';
        }
    }
    return stars;
}

function formatPrice(price) {
    return new Intl.NumberFormat('en-RW').format(price);
}

function showError(message) {
    document.getElementById('products-container').innerHTML = `
        <div style="grid-column: 1 / -1; text-center; padding: 3rem;">
            <i class="fas fa-exclamation-triangle" style="font-size: 3rem; color: #f44336; margin-bottom: 1rem;"></i>
            <h4>Error</h4>
            <p style="color: var(--gray-dark);">${message}</p>
            <button class="btn btn-primary mt-2" onclick="loadProducts()">
                <i class="fas fa-refresh"></i> Try Again
            </button>
        </div>
    `;
}

function quickView(productId) {
    // Quick view functionality
    console.log('Quick view for product:', productId);
    showNotification('Quick view feature coming soon!', 'info');
}
</script>

<?php include 'includes/footer.php'; ?>
