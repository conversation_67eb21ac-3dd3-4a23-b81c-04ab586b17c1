<?php
/**
 * Wishlist API Endpoint
 * Handles wishlist operations for logged-in users
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

session_start();

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetRequest($db);
            break;
        case 'POST':
            handlePostRequest($db);
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}

function handleGetRequest($db) {
    $action = $_GET['action'] ?? 'list';
    
    switch ($action) {
        case 'list':
            getWishlistItems($db);
            break;
        case 'count':
            getWishlistCount($db);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
}

function handlePostRequest($db) {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'add':
            addToWishlist($db, $input);
            break;
        case 'remove':
            removeFromWishlist($db, $input);
            break;
        case 'clear':
            clearWishlist($db);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
}

function getWishlistItems($db) {
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'message' => 'User not logged in']);
        return;
    }
    
    $user_id = $_SESSION['user_id'];
    
    $query = "
        SELECT 
            wi.*,
            p.name as product_name,
            p.slug as product_slug,
            p.price as product_price,
            p.compare_price,
            p.stock_quantity,
            p.rating,
            p.total_reviews,
            v.first_name as vendor_first_name,
            v.last_name as vendor_last_name,
            vp.business_name as vendor_business_name,
            (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as product_image
        FROM wishlist_items wi
        LEFT JOIN products p ON wi.product_id = p.id
        LEFT JOIN users v ON p.vendor_id = v.id
        LEFT JOIN vendor_profiles vp ON v.id = vp.user_id
        WHERE wi.user_id = :user_id AND p.status = 'active'
        ORDER BY wi.created_at DESC
    ";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->execute();
    
    $wishlist_items = $stmt->fetchAll();
    
    $formatted_items = [];
    
    foreach ($wishlist_items as $item) {
        $formatted_items[] = [
            'id' => (int)$item['id'],
            'product_id' => (int)$item['product_id'],
            'product_name' => $item['product_name'],
            'product_slug' => $item['product_slug'],
            'product_price' => (float)$item['product_price'],
            'compare_price' => $item['compare_price'] ? (float)$item['compare_price'] : null,
            'stock_quantity' => (int)$item['stock_quantity'],
            'rating' => (float)$item['rating'],
            'total_reviews' => (int)$item['total_reviews'],
            'vendor_name' => $item['vendor_business_name'] ?: 
                           ($item['vendor_first_name'] . ' ' . $item['vendor_last_name']),
            'product_image' => $item['product_image'] ? BASE_URL . 'uploads/' . $item['product_image'] : null,
            'added_at' => $item['created_at']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'items' => $formatted_items,
        'total_items' => count($formatted_items)
    ]);
}

function getWishlistCount($db) {
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => true, 'count' => 0]);
        return;
    }
    
    $user_id = $_SESSION['user_id'];
    
    $query = "
        SELECT COUNT(*) as total_count
        FROM wishlist_items wi
        LEFT JOIN products p ON wi.product_id = p.id
        WHERE wi.user_id = :user_id AND p.status = 'active'
    ";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->execute();
    
    $result = $stmt->fetch();
    $count = $result ? (int)$result['total_count'] : 0;
    
    echo json_encode([
        'success' => true,
        'count' => $count
    ]);
}

function addToWishlist($db, $input) {
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'message' => 'User not logged in']);
        return;
    }
    
    $user_id = $_SESSION['user_id'];
    $product_id = $input['product_id'] ?? 0;
    
    if (!$product_id) {
        echo json_encode(['success' => false, 'message' => 'Product ID is required']);
        return;
    }
    
    // Check if product exists and is active
    $product_query = "SELECT id, name FROM products WHERE id = :product_id AND status = 'active'";
    $product_stmt = $db->prepare($product_query);
    $product_stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
    $product_stmt->execute();
    
    $product = $product_stmt->fetch();
    
    if (!$product) {
        echo json_encode(['success' => false, 'message' => 'Product not found or unavailable']);
        return;
    }
    
    // Check if item already exists in wishlist
    $check_query = "SELECT id FROM wishlist_items WHERE user_id = :user_id AND product_id = :product_id";
    $check_stmt = $db->prepare($check_query);
    $check_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $check_stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
    $check_stmt->execute();
    
    if ($check_stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'Product already in wishlist']);
        return;
    }
    
    // Add to wishlist
    $insert_query = "INSERT INTO wishlist_items (user_id, product_id) VALUES (:user_id, :product_id)";
    $insert_stmt = $db->prepare($insert_query);
    $insert_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $insert_stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
    
    if ($insert_stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Product added to wishlist successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to add product to wishlist']);
    }
}

function removeFromWishlist($db, $input) {
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'message' => 'User not logged in']);
        return;
    }
    
    $user_id = $_SESSION['user_id'];
    $product_id = $input['product_id'] ?? 0;
    
    if (!$product_id) {
        echo json_encode(['success' => false, 'message' => 'Product ID is required']);
        return;
    }
    
    $delete_query = "DELETE FROM wishlist_items WHERE user_id = :user_id AND product_id = :product_id";
    $delete_stmt = $db->prepare($delete_query);
    $delete_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $delete_stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
    
    if ($delete_stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Product removed from wishlist']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to remove product from wishlist']);
    }
}

function clearWishlist($db) {
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'message' => 'User not logged in']);
        return;
    }
    
    $user_id = $_SESSION['user_id'];
    
    $delete_query = "DELETE FROM wishlist_items WHERE user_id = :user_id";
    $delete_stmt = $db->prepare($delete_query);
    $delete_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    
    if ($delete_stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Wishlist cleared successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to clear wishlist']);
    }
}
?>
