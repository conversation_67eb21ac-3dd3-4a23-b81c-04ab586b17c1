<?php
/**
 * Vendor Dashboard API Endpoint
 * Provides dashboard data for authenticated vendors
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';
require_once '../includes/vendor-auth.php';

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Require vendor authentication
if (!$vendorAuth->requireVerifiedVendor(false)) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $vendor_id = $vendorAuth->getVendorId();
    $action = $_GET['action'] ?? 'overview';
    
    switch ($action) {
        case 'overview':
            getVendorOverview($db, $vendor_id);
            break;
        case 'stats':
            getVendorStats($db, $vendor_id);
            break;
        case 'recent_orders':
            getRecentOrders($db, $vendor_id);
            break;
        case 'recent_products':
            getRecentProducts($db, $vendor_id);
            break;
        case 'analytics':
            getVendorAnalytics($db, $vendor_id);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}

function getVendorOverview($db, $vendor_id) {
    // Get comprehensive vendor overview data
    $overview = [
        'stats' => getVendorStatsData($db, $vendor_id),
        'recent_orders' => getRecentOrdersData($db, $vendor_id, 5),
        'recent_products' => getRecentProductsData($db, $vendor_id, 5),
        'notifications' => getVendorNotifications($db, $vendor_id),
        'performance' => getPerformanceMetrics($db, $vendor_id)
    ];
    
    echo json_encode([
        'success' => true,
        'overview' => $overview
    ]);
}

function getVendorStats($db, $vendor_id) {
    $stats = getVendorStatsData($db, $vendor_id);
    
    echo json_encode([
        'success' => true,
        'stats' => $stats
    ]);
}

function getVendorStatsData($db, $vendor_id) {
    // Get total products
    $products_query = "SELECT COUNT(*) as total_products FROM products WHERE vendor_id = :vendor_id AND status != 'deleted'";
    $products_stmt = $db->prepare($products_query);
    $products_stmt->bindParam(':vendor_id', $vendor_id);
    $products_stmt->execute();
    $total_products = $products_stmt->fetch()['total_products'] ?? 0;
    
    // Get active products
    $active_products_query = "SELECT COUNT(*) as active_products FROM products WHERE vendor_id = :vendor_id AND status = 'active'";
    $active_products_stmt = $db->prepare($active_products_query);
    $active_products_stmt->bindParam(':vendor_id', $vendor_id);
    $active_products_stmt->execute();
    $active_products = $active_products_stmt->fetch()['active_products'] ?? 0;
    
    // Get total orders (from order_items joined with products)
    $orders_query = "
        SELECT COUNT(DISTINCT o.id) as total_orders
        FROM orders o
        JOIN order_items oi ON o.id = oi.order_id
        JOIN products p ON oi.product_id = p.id
        WHERE p.vendor_id = :vendor_id
    ";
    $orders_stmt = $db->prepare($orders_query);
    $orders_stmt->bindParam(':vendor_id', $vendor_id);
    $orders_stmt->execute();
    $total_orders = $orders_stmt->fetch()['total_orders'] ?? 0;
    
    // Get pending orders
    $pending_orders_query = "
        SELECT COUNT(DISTINCT o.id) as pending_orders
        FROM orders o
        JOIN order_items oi ON o.id = oi.order_id
        JOIN products p ON oi.product_id = p.id
        WHERE p.vendor_id = :vendor_id AND o.status IN ('pending', 'confirmed')
    ";
    $pending_orders_stmt = $db->prepare($pending_orders_query);
    $pending_orders_stmt->bindParam(':vendor_id', $vendor_id);
    $pending_orders_stmt->execute();
    $pending_orders = $pending_orders_stmt->fetch()['pending_orders'] ?? 0;
    
    // Get total revenue
    $revenue_query = "
        SELECT COALESCE(SUM(oi.price * oi.quantity), 0) as total_revenue
        FROM order_items oi
        JOIN products p ON oi.product_id = p.id
        JOIN orders o ON oi.order_id = o.id
        WHERE p.vendor_id = :vendor_id AND o.status IN ('completed', 'delivered')
    ";
    $revenue_stmt = $db->prepare($revenue_query);
    $revenue_stmt->bindParam(':vendor_id', $vendor_id);
    $revenue_stmt->execute();
    $total_revenue = $revenue_stmt->fetch()['total_revenue'] ?? 0;
    
    // Get this month's revenue
    $monthly_revenue_query = "
        SELECT COALESCE(SUM(oi.price * oi.quantity), 0) as monthly_revenue
        FROM order_items oi
        JOIN products p ON oi.product_id = p.id
        JOIN orders o ON oi.order_id = o.id
        WHERE p.vendor_id = :vendor_id 
        AND o.status IN ('completed', 'delivered')
        AND MONTH(o.created_at) = MONTH(CURRENT_DATE())
        AND YEAR(o.created_at) = YEAR(CURRENT_DATE())
    ";
    $monthly_revenue_stmt = $db->prepare($monthly_revenue_query);
    $monthly_revenue_stmt->bindParam(':vendor_id', $vendor_id);
    $monthly_revenue_stmt->execute();
    $monthly_revenue = $monthly_revenue_stmt->fetch()['monthly_revenue'] ?? 0;
    
    // Get average rating
    $rating_query = "
        SELECT AVG(pr.rating) as avg_rating, COUNT(pr.id) as total_reviews
        FROM product_reviews pr
        JOIN products p ON pr.product_id = p.id
        WHERE p.vendor_id = :vendor_id AND pr.status = 'approved'
    ";
    $rating_stmt = $db->prepare($rating_query);
    $rating_stmt->bindParam(':vendor_id', $vendor_id);
    $rating_stmt->execute();
    $rating_data = $rating_stmt->fetch();
    $average_rating = $rating_data['avg_rating'] ? round($rating_data['avg_rating'], 1) : 0;
    $total_reviews = $rating_data['total_reviews'] ?? 0;
    
    return [
        'total_products' => (int)$total_products,
        'active_products' => (int)$active_products,
        'total_orders' => (int)$total_orders,
        'pending_orders' => (int)$pending_orders,
        'total_revenue' => (float)$total_revenue,
        'monthly_revenue' => (float)$monthly_revenue,
        'average_rating' => (float)$average_rating,
        'total_reviews' => (int)$total_reviews
    ];
}

function getRecentOrders($db, $vendor_id) {
    $orders = getRecentOrdersData($db, $vendor_id, 10);
    
    echo json_encode([
        'success' => true,
        'orders' => $orders
    ]);
}

function getRecentOrdersData($db, $vendor_id, $limit = 5) {
    $query = "
        SELECT DISTINCT
            o.id,
            o.order_number,
            o.status,
            o.total_amount,
            o.created_at,
            u.first_name,
            u.last_name,
            u.email,
            COUNT(oi.id) as item_count
        FROM orders o
        JOIN order_items oi ON o.id = oi.order_id
        JOIN products p ON oi.product_id = p.id
        JOIN users u ON o.user_id = u.id
        WHERE p.vendor_id = :vendor_id
        GROUP BY o.id
        ORDER BY o.created_at DESC
        LIMIT :limit
    ";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':vendor_id', $vendor_id);
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    
    $orders = $stmt->fetchAll();
    
    return array_map(function($order) {
        return [
            'id' => (int)$order['id'],
            'order_number' => $order['order_number'],
            'status' => $order['status'],
            'total_amount' => (float)$order['total_amount'],
            'item_count' => (int)$order['item_count'],
            'customer_name' => $order['first_name'] . ' ' . $order['last_name'],
            'customer_email' => $order['email'],
            'created_at' => $order['created_at']
        ];
    }, $orders);
}

function getRecentProducts($db, $vendor_id) {
    $products = getRecentProductsData($db, $vendor_id, 10);
    
    echo json_encode([
        'success' => true,
        'products' => $products
    ]);
}

function getRecentProductsData($db, $vendor_id, $limit = 5) {
    $query = "
        SELECT 
            p.*,
            c.name as category_name,
            (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.vendor_id = :vendor_id AND p.status != 'deleted'
        ORDER BY p.created_at DESC
        LIMIT :limit
    ";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':vendor_id', $vendor_id);
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    
    $products = $stmt->fetchAll();
    
    return array_map(function($product) {
        return [
            'id' => (int)$product['id'],
            'name' => $product['name'],
            'slug' => $product['slug'],
            'price' => (float)$product['price'],
            'stock_quantity' => (int)$product['stock_quantity'],
            'status' => $product['status'],
            'featured' => (bool)$product['featured'],
            'category_name' => $product['category_name'],
            'image' => $product['primary_image'] ? BASE_URL . 'uploads/' . $product['primary_image'] : null,
            'created_at' => $product['created_at']
        ];
    }, $products);
}

function getVendorNotifications($db, $vendor_id) {
    // Get important notifications for vendor
    $notifications = [];
    
    // Check for low stock products
    $low_stock_query = "
        SELECT COUNT(*) as low_stock_count
        FROM products 
        WHERE vendor_id = :vendor_id 
        AND status = 'active' 
        AND stock_quantity <= 5
    ";
    $low_stock_stmt = $db->prepare($low_stock_query);
    $low_stock_stmt->bindParam(':vendor_id', $vendor_id);
    $low_stock_stmt->execute();
    $low_stock_count = $low_stock_stmt->fetch()['low_stock_count'] ?? 0;
    
    if ($low_stock_count > 0) {
        $notifications[] = [
            'type' => 'warning',
            'title' => 'Low Stock Alert',
            'message' => "$low_stock_count product(s) are running low on stock",
            'action_url' => BASE_URL . 'vendor-dashboard#products',
            'created_at' => date('Y-m-d H:i:s')
        ];
    }
    
    // Check for pending orders
    $pending_orders_query = "
        SELECT COUNT(DISTINCT o.id) as pending_count
        FROM orders o
        JOIN order_items oi ON o.id = oi.order_id
        JOIN products p ON oi.product_id = p.id
        WHERE p.vendor_id = :vendor_id AND o.status = 'pending'
    ";
    $pending_orders_stmt = $db->prepare($pending_orders_query);
    $pending_orders_stmt->bindParam(':vendor_id', $vendor_id);
    $pending_orders_stmt->execute();
    $pending_count = $pending_orders_stmt->fetch()['pending_count'] ?? 0;
    
    if ($pending_count > 0) {
        $notifications[] = [
            'type' => 'info',
            'title' => 'New Orders',
            'message' => "$pending_count new order(s) require your attention",
            'action_url' => BASE_URL . 'vendor-dashboard#orders',
            'created_at' => date('Y-m-d H:i:s')
        ];
    }
    
    return $notifications;
}

function getPerformanceMetrics($db, $vendor_id) {
    // Calculate performance metrics
    $metrics = [];
    
    // Sales growth (this month vs last month)
    $current_month_query = "
        SELECT COALESCE(SUM(oi.price * oi.quantity), 0) as current_month_sales
        FROM order_items oi
        JOIN products p ON oi.product_id = p.id
        JOIN orders o ON oi.order_id = o.id
        WHERE p.vendor_id = :vendor_id 
        AND o.status IN ('completed', 'delivered')
        AND MONTH(o.created_at) = MONTH(CURRENT_DATE())
        AND YEAR(o.created_at) = YEAR(CURRENT_DATE())
    ";
    
    $last_month_query = "
        SELECT COALESCE(SUM(oi.price * oi.quantity), 0) as last_month_sales
        FROM order_items oi
        JOIN products p ON oi.product_id = p.id
        JOIN orders o ON oi.order_id = o.id
        WHERE p.vendor_id = :vendor_id 
        AND o.status IN ('completed', 'delivered')
        AND MONTH(o.created_at) = MONTH(CURRENT_DATE() - INTERVAL 1 MONTH)
        AND YEAR(o.created_at) = YEAR(CURRENT_DATE() - INTERVAL 1 MONTH)
    ";
    
    $current_stmt = $db->prepare($current_month_query);
    $current_stmt->bindParam(':vendor_id', $vendor_id);
    $current_stmt->execute();
    $current_month_sales = $current_stmt->fetch()['current_month_sales'] ?? 0;
    
    $last_stmt = $db->prepare($last_month_query);
    $last_stmt->bindParam(':vendor_id', $vendor_id);
    $last_stmt->execute();
    $last_month_sales = $last_stmt->fetch()['last_month_sales'] ?? 0;
    
    $sales_growth = 0;
    if ($last_month_sales > 0) {
        $sales_growth = (($current_month_sales - $last_month_sales) / $last_month_sales) * 100;
    } elseif ($current_month_sales > 0) {
        $sales_growth = 100; // 100% growth from 0
    }
    
    return [
        'sales_growth' => round($sales_growth, 1),
        'current_month_sales' => (float)$current_month_sales,
        'last_month_sales' => (float)$last_month_sales
    ];
}

function getVendorAnalytics($db, $vendor_id) {
    // Get detailed analytics data
    $analytics = [
        'sales_by_month' => getSalesByMonth($db, $vendor_id),
        'top_products' => getTopProducts($db, $vendor_id),
        'order_status_breakdown' => getOrderStatusBreakdown($db, $vendor_id)
    ];
    
    echo json_encode([
        'success' => true,
        'analytics' => $analytics
    ]);
}

function getSalesByMonth($db, $vendor_id) {
    $query = "
        SELECT 
            DATE_FORMAT(o.created_at, '%Y-%m') as month,
            COALESCE(SUM(oi.price * oi.quantity), 0) as sales
        FROM order_items oi
        JOIN products p ON oi.product_id = p.id
        JOIN orders o ON oi.order_id = o.id
        WHERE p.vendor_id = :vendor_id 
        AND o.status IN ('completed', 'delivered')
        AND o.created_at >= DATE_SUB(CURRENT_DATE(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(o.created_at, '%Y-%m')
        ORDER BY month ASC
    ";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':vendor_id', $vendor_id);
    $stmt->execute();
    
    return $stmt->fetchAll();
}

function getTopProducts($db, $vendor_id) {
    $query = "
        SELECT 
            p.name,
            p.price,
            COALESCE(SUM(oi.quantity), 0) as total_sold,
            COALESCE(SUM(oi.price * oi.quantity), 0) as total_revenue
        FROM products p
        LEFT JOIN order_items oi ON p.id = oi.product_id
        LEFT JOIN orders o ON oi.order_id = o.id
        WHERE p.vendor_id = :vendor_id 
        AND (o.status IN ('completed', 'delivered') OR o.status IS NULL)
        GROUP BY p.id
        ORDER BY total_sold DESC
        LIMIT 10
    ";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':vendor_id', $vendor_id);
    $stmt->execute();
    
    return $stmt->fetchAll();
}

function getOrderStatusBreakdown($db, $vendor_id) {
    $query = "
        SELECT 
            o.status,
            COUNT(DISTINCT o.id) as count
        FROM orders o
        JOIN order_items oi ON o.id = oi.order_id
        JOIN products p ON oi.product_id = p.id
        WHERE p.vendor_id = :vendor_id
        GROUP BY o.status
        ORDER BY count DESC
    ";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':vendor_id', $vendor_id);
    $stmt->execute();
    
    return $stmt->fetchAll();
}
?>
