<?php
$current_page = '404';
$page_title = 'Page Not Found';
$page_description = 'The page you are looking for could not be found';

include 'includes/header.php';
?>

<div class="container" style="padding: 4rem 0;">
    <div class="row justify-center">
        <div class="col col-8 text-center">
            <div class="error-404">
                <!-- 404 Illustration -->
                <div class="error-illustration" style="margin-bottom: 2rem;">
                    <div style="font-size: 8rem; font-weight: 700; color: var(--primary-green); line-height: 1;">
                        4<i class="fas fa-search" style="font-size: 6rem; color: var(--secondary-green); margin: 0 1rem;"></i>4
                    </div>
                </div>
                
                <!-- Error Message -->
                <h1 style="font-size: 2.5rem; margin-bottom: 1rem; color: var(--black);">
                    Oops! Page Not Found
                </h1>
                
                <p style="font-size: 1.2rem; color: var(--gray-dark); margin-bottom: 2rem; max-width: 600px; margin-left: auto; margin-right: auto;">
                    The page you're looking for doesn't exist or has been moved. 
                    Don't worry, let's get you back on track!
                </p>
                
                <!-- Action Buttons -->
                <div class="error-actions" style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap; margin-bottom: 3rem;">
                    <a href="<?php echo BASE_URL; ?>" class="btn btn-primary btn-lg">
                        <i class="fas fa-home"></i> Go Home
                    </a>
                    <a href="<?php echo BASE_URL; ?>products" class="btn btn-secondary btn-lg">
                        <i class="fas fa-shopping-bag"></i> Browse Products
                    </a>
                    <button class="btn btn-secondary btn-lg" onclick="goBack()">
                        <i class="fas fa-arrow-left"></i> Go Back
                    </button>
                </div>
                
                <!-- Search Box -->
                <div class="error-search" style="max-width: 500px; margin: 0 auto 3rem;">
                    <h5 style="margin-bottom: 1rem; color: var(--primary-green);">
                        <i class="fas fa-search"></i> Search for what you need
                    </h5>
                    <form action="<?php echo BASE_URL; ?>products" method="GET" style="display: flex; gap: 0.5rem;">
                        <input type="text" 
                               name="search" 
                               class="form-control" 
                               placeholder="Search products, vendors, or categories..."
                               style="flex: 1;">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
                
                <!-- Popular Links -->
                <div class="popular-links">
                    <h5 style="margin-bottom: 1.5rem; color: var(--primary-green);">
                        <i class="fas fa-fire"></i> Popular Pages
                    </h5>
                    <div class="links-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; max-width: 800px; margin: 0 auto;">
                        <a href="<?php echo BASE_URL; ?>products?category=electronics" class="popular-link">
                            <i class="fas fa-laptop"></i>
                            <span>Electronics</span>
                        </a>
                        <a href="<?php echo BASE_URL; ?>products?category=clothing-fashion" class="popular-link">
                            <i class="fas fa-tshirt"></i>
                            <span>Fashion</span>
                        </a>
                        <a href="<?php echo BASE_URL; ?>products?category=home-garden" class="popular-link">
                            <i class="fas fa-home"></i>
                            <span>Home & Garden</span>
                        </a>
                        <a href="<?php echo BASE_URL; ?>products?category=food-beverages" class="popular-link">
                            <i class="fas fa-utensils"></i>
                            <span>Food & Drinks</span>
                        </a>
                        <a href="<?php echo BASE_URL; ?>vendor-register" class="popular-link">
                            <i class="fas fa-store"></i>
                            <span>Become a Vendor</span>
                        </a>
                        <a href="<?php echo BASE_URL; ?>compare" class="popular-link">
                            <i class="fas fa-balance-scale"></i>
                            <span>Compare Products</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Help Section -->
<section style="background: var(--gray-light); padding: 3rem 0; margin-top: 2rem;">
    <div class="container">
        <div class="row">
            <div class="col col-4">
                <div class="help-card text-center">
                    <div style="background: var(--light-green); width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem;">
                        <i class="fas fa-headset" style="font-size: 2rem; color: var(--primary-green);"></i>
                    </div>
                    <h5>Need Help?</h5>
                    <p style="color: var(--gray-dark);">Our support team is here to assist you</p>
                    <a href="mailto:<EMAIL>" class="btn btn-secondary">
                        <i class="fas fa-envelope"></i> Contact Support
                    </a>
                </div>
            </div>
            
            <div class="col col-4">
                <div class="help-card text-center">
                    <div style="background: var(--light-green); width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem;">
                        <i class="fas fa-question-circle" style="font-size: 2rem; color: var(--primary-green);"></i>
                    </div>
                    <h5>FAQ</h5>
                    <p style="color: var(--gray-dark);">Find answers to commonly asked questions</p>
                    <a href="<?php echo BASE_URL; ?>faq" class="btn btn-secondary">
                        <i class="fas fa-book"></i> View FAQ
                    </a>
                </div>
            </div>
            
            <div class="col col-4">
                <div class="help-card text-center">
                    <div style="background: var(--light-green); width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem;">
                        <i class="fas fa-comments" style="font-size: 2rem; color: var(--primary-green);"></i>
                    </div>
                    <h5>Live Chat</h5>
                    <p style="color: var(--gray-dark);">Chat with us in real-time</p>
                    <button class="btn btn-secondary" onclick="openLiveChat()">
                        <i class="fas fa-comment"></i> Start Chat
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.error-404 {
    animation: fadeInUp 0.6s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.error-illustration {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.popular-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1.5rem;
    background: var(--white);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--gray-dark);
    transition: var(--transition);
    box-shadow: var(--shadow);
}

.popular-link:hover {
    background: var(--light-green);
    color: var(--primary-green);
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.popular-link i {
    font-size: 1.5rem;
    color: var(--primary-green);
}

.popular-link span {
    font-weight: 500;
}

.help-card {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.help-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.help-card h5 {
    color: var(--primary-green);
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .error-illustration div {
        font-size: 4rem;
    }
    
    .error-illustration i {
        font-size: 3rem !important;
        margin: 0 0.5rem !important;
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .error-actions .btn {
        width: 100%;
        max-width: 300px;
    }
    
    .links-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }
    
    .row {
        flex-direction: column;
    }
    
    .col {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

@media (max-width: 480px) {
    .links-grid {
        grid-template-columns: 1fr !important;
    }
    
    .error-search form {
        flex-direction: column;
    }
    
    .error-search .btn {
        width: 100%;
    }
}
</style>

<script>
function goBack() {
    if (window.history.length > 1) {
        window.history.back();
    } else {
        window.location.href = '<?php echo BASE_URL; ?>';
    }
}

function openLiveChat() {
    // Simulate live chat functionality
    showNotification('Live chat feature coming soon! Please contact us via email for now.', 'info');
}

// Show notification function
function showNotification(message, type = 'info', duration = 5000) {
    let container = document.getElementById('notification-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notification-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
        `;
        document.body.appendChild(container);
    }
    
    const notification = document.createElement('div');
    
    const colors = {
        success: '#4CAF50',
        error: '#f44336',
        warning: '#ff9800',
        info: '#2196F3'
    };
    
    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };
    
    notification.style.cssText = `
        background: ${colors[type]};
        color: white;
        padding: 15px 20px;
        margin-bottom: 10px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        display: flex;
        align-items: center;
        gap: 10px;
        animation: slideIn 0.3s ease;
        cursor: pointer;
    `;
    
    notification.innerHTML = `
        <i class="${icons[type]}"></i>
        <span>${message}</span>
        <i class="fas fa-times" style="margin-left: auto; cursor: pointer;"></i>
    `;
    
    notification.addEventListener('click', () => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    });
    
    container.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }
    }, duration);
}

// Add some interactive elements
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effect to the 404 number
    const errorIllustration = document.querySelector('.error-illustration div');
    if (errorIllustration) {
        errorIllustration.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
            this.style.transition = 'transform 0.3s ease';
        });
        
        errorIllustration.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    }
});
</script>

<?php include 'includes/footer.php'; ?>
