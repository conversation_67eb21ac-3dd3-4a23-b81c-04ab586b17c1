<?php
$current_page = 'register';
$page_title = 'Register';
$page_description = 'Create your Musanze Marketplace account';

// Redirect if already logged in
session_start();
if (isset($_SESSION['user_id'])) {
    $redirect_url = $_SESSION['user_type'] === 'vendor' ? 'vendor-dashboard' : 
                   ($_SESSION['user_type'] === 'admin' ? 'admin' : 'dashboard');
    header('Location: ' . BASE_URL . $redirect_url);
    exit;
}

include '../includes/header.php';
?>

<div class="container" style="padding: 3rem 0;">
    <div class="row justify-center">
        <div class="col col-8" style="max-width: 600px;">
            <div class="card">
                <div class="card-header text-center">
                    <h3><i class="fas fa-user-plus" style="color: var(--primary-green);"></i> Create Your Account</h3>
                    <p style="color: var(--gray-dark); margin: 0;">Join Musanze Marketplace today</p>
                </div>
                
                <div class="card-body">
                    <form id="register-form">
                        <div class="row">
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-user"></i> First Name *
                                    </label>
                                    <input type="text" 
                                           name="first_name" 
                                           class="form-control" 
                                           placeholder="Enter your first name"
                                           required>
                                </div>
                            </div>
                            
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-user"></i> Last Name *
                                    </label>
                                    <input type="text" 
                                           name="last_name" 
                                           class="form-control" 
                                           placeholder="Enter your last name"
                                           required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-at"></i> Username *
                            </label>
                            <input type="text" 
                                   name="username" 
                                   class="form-control" 
                                   placeholder="Choose a username"
                                   required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-envelope"></i> Email Address *
                            </label>
                            <input type="email" 
                                   name="email" 
                                   class="form-control" 
                                   placeholder="Enter your email"
                                   required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-phone"></i> Phone Number
                            </label>
                            <input type="tel" 
                                   name="phone" 
                                   class="form-control" 
                                   placeholder="+250 788 123 456">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-map-marker-alt"></i> Address
                            </label>
                            <textarea name="address" 
                                      class="form-control" 
                                      rows="2" 
                                      placeholder="Enter your address"></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-lock"></i> Password *
                                    </label>
                                    <div style="position: relative;">
                                        <input type="password" 
                                               name="password" 
                                               class="form-control" 
                                               placeholder="Create a password"
                                               id="password-input"
                                               required>
                                        <button type="button" 
                                                class="password-toggle"
                                                onclick="togglePassword('password-input', 'password-toggle-icon')"
                                                style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); background: none; border: none; color: var(--gray-dark); cursor: pointer;">
                                            <i class="fas fa-eye" id="password-toggle-icon"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-lock"></i> Confirm Password *
                                    </label>
                                    <div style="position: relative;">
                                        <input type="password" 
                                               name="confirm_password" 
                                               class="form-control" 
                                               placeholder="Confirm your password"
                                               id="confirm-password-input"
                                               required>
                                        <button type="button" 
                                                class="password-toggle"
                                                onclick="togglePassword('confirm-password-input', 'confirm-password-toggle-icon')"
                                                style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); background: none; border: none; color: var(--gray-dark); cursor: pointer;">
                                            <i class="fas fa-eye" id="confirm-password-toggle-icon"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label style="display: flex; align-items: flex-start; margin: 0;">
                                <input type="checkbox" name="terms" style="margin-right: 0.5rem; margin-top: 0.2rem;" required>
                                <span style="font-size: 0.9rem;">
                                    I agree to the <a href="<?php echo BASE_URL; ?>terms" style="color: var(--primary-green);">Terms of Service</a> 
                                    and <a href="<?php echo BASE_URL; ?>privacy" style="color: var(--primary-green);">Privacy Policy</a>
                                </span>
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 btn-lg" id="register-btn">
                            <i class="fas fa-user-plus"></i> Create Account
                        </button>
                    </form>
                    
                    <div class="text-center mt-3">
                        <p style="color: var(--gray-dark);">
                            Already have an account? 
                            <a href="<?php echo BASE_URL; ?>login" style="color: var(--primary-green); font-weight: 500;">
                                Login here
                            </a>
                        </p>
                        <p style="color: var(--gray-dark);">
                            Want to sell your products? 
                            <a href="<?php echo BASE_URL; ?>vendor-register" style="color: var(--secondary-green); font-weight: 500;">
                                Become a Vendor
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const registerForm = document.getElementById('register-form');
    const registerBtn = document.getElementById('register-btn');
    
    registerForm.addEventListener('submit', function(e) {
        e.preventDefault();
        handleRegister();
    });
    
    // Real-time password validation
    const passwordInput = document.querySelector('input[name="password"]');
    const confirmPasswordInput = document.querySelector('input[name="confirm_password"]');
    
    confirmPasswordInput.addEventListener('input', function() {
        if (this.value && passwordInput.value !== this.value) {
            this.setCustomValidity('Passwords do not match');
        } else {
            this.setCustomValidity('');
        }
    });
});

function handleRegister() {
    const form = document.getElementById('register-form');
    const formData = new FormData(form);
    const registerBtn = document.getElementById('register-btn');
    
    // Validate passwords match
    if (formData.get('password') !== formData.get('confirm_password')) {
        showNotification('Passwords do not match', 'error');
        return;
    }
    
    // Disable button and show loading
    registerBtn.disabled = true;
    registerBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Account...';
    
    const data = {
        action: 'register',
        username: formData.get('username'),
        email: formData.get('email'),
        password: formData.get('password'),
        confirm_password: formData.get('confirm_password'),
        first_name: formData.get('first_name'),
        last_name: formData.get('last_name'),
        phone: formData.get('phone'),
        address: formData.get('address')
    };
    
    fetch('<?php echo BASE_URL; ?>api/auth.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            
            // Redirect after short delay
            setTimeout(() => {
                window.location.href = data.redirect || '<?php echo BASE_URL; ?>dashboard';
            }, 1500);
        } else {
            showNotification(data.message, 'error');
            
            // Re-enable button
            registerBtn.disabled = false;
            registerBtn.innerHTML = '<i class="fas fa-user-plus"></i> Create Account';
        }
    })
    .catch(error => {
        console.error('Registration error:', error);
        showNotification('An error occurred. Please try again.', 'error');
        
        // Re-enable button
        registerBtn.disabled = false;
        registerBtn.innerHTML = '<i class="fas fa-user-plus"></i> Create Account';
    });
}

function togglePassword(inputId, iconId) {
    const passwordInput = document.getElementById(inputId);
    const toggleIcon = document.getElementById(iconId);
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleIcon.className = 'fas fa-eye';
    }
}

// Show notification function
function showNotification(message, type = 'info', duration = 5000) {
    let container = document.getElementById('notification-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notification-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
        `;
        document.body.appendChild(container);
    }
    
    const notification = document.createElement('div');
    
    const colors = {
        success: '#4CAF50',
        error: '#f44336',
        warning: '#ff9800',
        info: '#2196F3'
    };
    
    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };
    
    notification.style.cssText = `
        background: ${colors[type]};
        color: white;
        padding: 15px 20px;
        margin-bottom: 10px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        display: flex;
        align-items: center;
        gap: 10px;
        animation: slideIn 0.3s ease;
        cursor: pointer;
    `;
    
    notification.innerHTML = `
        <i class="${icons[type]}"></i>
        <span>${message}</span>
        <i class="fas fa-times" style="margin-left: auto; cursor: pointer;"></i>
    `;
    
    notification.addEventListener('click', () => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    });
    
    container.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }
    }, duration);
}
</script>

<style>
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 2rem 0;
    }
    
    .col {
        max-width: none !important;
    }
    
    .row .col {
        flex: 0 0 100%;
    }
}
</style>

<?php include '../includes/footer.php'; ?>
