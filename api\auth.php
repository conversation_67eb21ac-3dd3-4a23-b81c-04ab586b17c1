<?php
/**
 * Authentication API Endpoint
 * Handles user login, registration, and session management
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

session_start();

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetRequest($db);
            break;
        case 'POST':
            handlePostRequest($db);
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}

function handleGetRequest($db) {
    $action = $_GET['action'] ?? 'status';
    
    switch ($action) {
        case 'status':
            checkAuthStatus();
            break;
        case 'logout':
            handleLogout();
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
}

function handlePostRequest($db) {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'login':
            handleLogin($db, $input);
            break;
        case 'register':
            handleRegister($db, $input);
            break;
        case 'vendor_register':
            handleVendorRegister($db, $input);
            break;
        case 'forgot_password':
            handleForgotPassword($db, $input);
            break;
        case 'reset_password':
            handleResetPassword($db, $input);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
}

function checkAuthStatus() {
    if (isset($_SESSION['user_id'])) {
        echo json_encode([
            'success' => true,
            'authenticated' => true,
            'user' => [
                'id' => $_SESSION['user_id'],
                'username' => $_SESSION['username'],
                'email' => $_SESSION['email'],
                'first_name' => $_SESSION['first_name'],
                'last_name' => $_SESSION['last_name'],
                'user_type' => $_SESSION['user_type']
            ]
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'authenticated' => false
        ]);
    }
}

function handleLogin($db, $input) {
    $email = trim($input['email'] ?? '');
    $password = $input['password'] ?? '';
    $remember = $input['remember'] ?? false;

    // Validate input
    if (empty($email) || empty($password)) {
        echo json_encode(['success' => false, 'message' => 'Email and password are required']);
        return;
    }

    // Check user credentials with vendor profile info
    $query = "
        SELECT
            u.*,
            vp.business_name,
            vp.verification_status,
            vp.business_phone,
            vp.business_email
        FROM users u
        LEFT JOIN vendor_profiles vp ON u.id = vp.user_id
        WHERE u.email = :email AND u.status = 'active'
    ";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':email', $email);
    $stmt->execute();

    $user = $stmt->fetch();

    if (!$user || !password_verify($password, $user['password'])) {
        // Log failed login attempt
        logLoginAttempt($db, $email, false, $_SERVER['REMOTE_ADDR']);
        echo json_encode(['success' => false, 'message' => 'Invalid email or password']);
        return;
    }

    // Check if vendor account is verified (for vendors only)
    if ($user['user_type'] === 'vendor') {
        if ($user['verification_status'] === 'pending') {
            echo json_encode([
                'success' => false,
                'message' => 'Your vendor account is pending approval. Please wait for admin verification.',
                'status' => 'pending_verification'
            ]);
            return;
        } elseif ($user['verification_status'] === 'rejected') {
            echo json_encode([
                'success' => false,
                'message' => 'Your vendor account has been rejected. Please contact support.',
                'status' => 'rejected'
            ]);
            return;
        }
    }

    // Create comprehensive session
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['email'] = $user['email'];
    $_SESSION['first_name'] = $user['first_name'];
    $_SESSION['last_name'] = $user['last_name'];
    $_SESSION['user_type'] = $user['user_type'];
    $_SESSION['phone'] = $user['phone'];
    $_SESSION['login_time'] = time();
    $_SESSION['last_activity'] = time();

    // Store vendor-specific session data
    if ($user['user_type'] === 'vendor') {
        $_SESSION['business_name'] = $user['business_name'];
        $_SESSION['verification_status'] = $user['verification_status'];
        $_SESSION['business_phone'] = $user['business_phone'];
        $_SESSION['business_email'] = $user['business_email'];
    }

    // Update last login and login count
    $update_query = "
        UPDATE users
        SET last_login = CURRENT_TIMESTAMP,
            login_count = COALESCE(login_count, 0) + 1,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = :user_id
    ";
    $update_stmt = $db->prepare($update_query);
    $update_stmt->bindParam(':user_id', $user['id']);
    $update_stmt->execute();

    // Log successful login
    logLoginAttempt($db, $email, true, $_SERVER['REMOTE_ADDR']);

    // Set remember me cookie if requested
    if ($remember) {
        $token = bin2hex(random_bytes(32));
        $expires = time() + (30 * 24 * 60 * 60); // 30 days
        setcookie('remember_token', $token, $expires, '/', '', false, true); // HttpOnly for security

        // Store remember token in database
        storeRememberToken($db, $user['id'], $token, $expires);
    }

    echo json_encode([
        'success' => true,
        'message' => 'Login successful',
        'user' => [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'first_name' => $user['first_name'],
            'last_name' => $user['last_name'],
            'user_type' => $user['user_type'],
            'business_name' => $user['business_name'] ?? null,
            'verification_status' => $user['verification_status'] ?? null
        ],
        'redirect' => getDashboardUrl($user['user_type'])
    ]);
}

function handleRegister($db, $input) {
    $username = trim($input['username'] ?? '');
    $email = trim($input['email'] ?? '');
    $password = $input['password'] ?? '';
    $confirm_password = $input['confirm_password'] ?? '';
    $first_name = trim($input['first_name'] ?? '');
    $last_name = trim($input['last_name'] ?? '');
    $phone = trim($input['phone'] ?? '');
    $address = trim($input['address'] ?? '');
    
    // Validate input
    $errors = [];
    
    if (empty($username)) $errors[] = 'Username is required';
    if (empty($email)) $errors[] = 'Email is required';
    if (empty($password)) $errors[] = 'Password is required';
    if (empty($first_name)) $errors[] = 'First name is required';
    if (empty($last_name)) $errors[] = 'Last name is required';
    
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email format';
    }
    
    if (strlen($password) < 6) {
        $errors[] = 'Password must be at least 6 characters';
    }
    
    if ($password !== $confirm_password) {
        $errors[] = 'Passwords do not match';
    }
    
    if (!empty($errors)) {
        echo json_encode(['success' => false, 'message' => implode(', ', $errors)]);
        return;
    }
    
    // Check if username or email already exists
    $check_query = "SELECT id FROM users WHERE username = :username OR email = :email";
    $check_stmt = $db->prepare($check_query);
    $check_stmt->bindParam(':username', $username);
    $check_stmt->bindParam(':email', $email);
    $check_stmt->execute();
    
    if ($check_stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'Username or email already exists']);
        return;
    }
    
    // Hash password
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
    
    // Insert user
    $insert_query = "
        INSERT INTO users (username, email, password, first_name, last_name, phone, address, user_type, status)
        VALUES (:username, :email, :password, :first_name, :last_name, :phone, :address, 'customer', 'active')
    ";
    
    $insert_stmt = $db->prepare($insert_query);
    $insert_stmt->bindParam(':username', $username);
    $insert_stmt->bindParam(':email', $email);
    $insert_stmt->bindParam(':password', $hashed_password);
    $insert_stmt->bindParam(':first_name', $first_name);
    $insert_stmt->bindParam(':last_name', $last_name);
    $insert_stmt->bindParam(':phone', $phone);
    $insert_stmt->bindParam(':address', $address);
    
    if ($insert_stmt->execute()) {
        $user_id = $db->lastInsertId();
        
        // Auto-login the user
        $_SESSION['user_id'] = $user_id;
        $_SESSION['username'] = $username;
        $_SESSION['email'] = $email;
        $_SESSION['first_name'] = $first_name;
        $_SESSION['last_name'] = $last_name;
        $_SESSION['user_type'] = 'customer';
        
        echo json_encode([
            'success' => true,
            'message' => 'Registration successful',
            'user' => [
                'id' => $user_id,
                'username' => $username,
                'email' => $email,
                'first_name' => $first_name,
                'last_name' => $last_name,
                'user_type' => 'customer'
            ],
            'redirect' => BASE_URL . 'dashboard'
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Registration failed']);
    }
}

function handleVendorRegister($db, $input) {
    // First register as regular user
    $user_data = [
        'username' => $input['username'],
        'email' => $input['email'],
        'password' => $input['password'],
        'confirm_password' => $input['confirm_password'],
        'first_name' => $input['first_name'],
        'last_name' => $input['last_name'],
        'phone' => $input['phone'],
        'address' => $input['address']
    ];
    
    // Vendor-specific data
    $business_name = trim($input['business_name'] ?? '');
    $business_description = trim($input['business_description'] ?? '');
    $business_address = trim($input['business_address'] ?? '');
    $business_phone = trim($input['business_phone'] ?? '');
    $business_email = trim($input['business_email'] ?? '');
    $tax_number = trim($input['tax_number'] ?? '');
    
    // Validate vendor-specific fields
    if (empty($business_name)) {
        echo json_encode(['success' => false, 'message' => 'Business name is required']);
        return;
    }
    
    // Start transaction
    $db->beginTransaction();
    
    try {
        // Register user first
        $username = trim($user_data['username']);
        $email = trim($user_data['email']);
        $password = $user_data['password'];
        $first_name = trim($user_data['first_name']);
        $last_name = trim($user_data['last_name']);
        $phone = trim($user_data['phone']);
        $address = trim($user_data['address']);
        
        // Check if username or email already exists
        $check_query = "SELECT id FROM users WHERE username = :username OR email = :email";
        $check_stmt = $db->prepare($check_query);
        $check_stmt->bindParam(':username', $username);
        $check_stmt->bindParam(':email', $email);
        $check_stmt->execute();
        
        if ($check_stmt->fetch()) {
            throw new Exception('Username or email already exists');
        }
        
        // Hash password
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        // Insert user
        $insert_user_query = "
            INSERT INTO users (username, email, password, first_name, last_name, phone, address, user_type, status)
            VALUES (:username, :email, :password, :first_name, :last_name, :phone, :address, 'vendor', 'pending')
        ";
        
        $insert_user_stmt = $db->prepare($insert_user_query);
        $insert_user_stmt->bindParam(':username', $username);
        $insert_user_stmt->bindParam(':email', $email);
        $insert_user_stmt->bindParam(':password', $hashed_password);
        $insert_user_stmt->bindParam(':first_name', $first_name);
        $insert_user_stmt->bindParam(':last_name', $last_name);
        $insert_user_stmt->bindParam(':phone', $phone);
        $insert_user_stmt->bindParam(':address', $address);
        
        $insert_user_stmt->execute();
        $user_id = $db->lastInsertId();
        
        // Insert vendor profile
        $insert_vendor_query = "
            INSERT INTO vendor_profiles 
            (user_id, business_name, business_description, business_address, business_phone, business_email, tax_number, verification_status)
            VALUES (:user_id, :business_name, :business_description, :business_address, :business_phone, :business_email, :tax_number, 'pending')
        ";
        
        $insert_vendor_stmt = $db->prepare($insert_vendor_query);
        $insert_vendor_stmt->bindParam(':user_id', $user_id);
        $insert_vendor_stmt->bindParam(':business_name', $business_name);
        $insert_vendor_stmt->bindParam(':business_description', $business_description);
        $insert_vendor_stmt->bindParam(':business_address', $business_address);
        $insert_vendor_stmt->bindParam(':business_phone', $business_phone);
        $insert_vendor_stmt->bindParam(':business_email', $business_email);
        $insert_vendor_stmt->bindParam(':tax_number', $tax_number);
        
        $insert_vendor_stmt->execute();
        
        $db->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Vendor registration successful. Your account is pending approval.',
            'redirect' => BASE_URL . 'login'
        ]);
        
    } catch (Exception $e) {
        $db->rollBack();
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

function handleLogout() {
    global $db;

    // Remove remember token from database if exists
    if (isset($_COOKIE['remember_token'])) {
        try {
            $token = $_COOKIE['remember_token'];
            $hashed_token = hash('sha256', $token);

            $query = "DELETE FROM remember_tokens WHERE token = :token";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':token', $hashed_token);
            $stmt->execute();
        } catch (Exception $e) {
            error_log('Remember token cleanup failed: ' . $e->getMessage());
        }

        // Clear remember me cookie
        setcookie('remember_token', '', time() - 3600, '/', '', false, true);
    }

    // Log logout activity
    if (isset($_SESSION['user_id'])) {
        try {
            $query = "
                INSERT INTO user_activity_log (user_id, activity_type, ip_address, created_at)
                VALUES (:user_id, 'logout', :ip_address, CURRENT_TIMESTAMP)
            ";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':user_id', $_SESSION['user_id']);
            $stmt->bindParam(':ip_address', $_SERVER['REMOTE_ADDR']);
            $stmt->execute();
        } catch (Exception $e) {
            error_log('Logout logging failed: ' . $e->getMessage());
        }
    }

    // Clear all session data
    $_SESSION = array();

    // Destroy session cookie
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }

    // Destroy session
    session_destroy();

    echo json_encode([
        'success' => true,
        'message' => 'Logged out successfully',
        'redirect' => BASE_URL
    ]);
}

function handleForgotPassword($db, $input) {
    $email = trim($input['email'] ?? '');
    
    if (empty($email)) {
        echo json_encode(['success' => false, 'message' => 'Email is required']);
        return;
    }
    
    // Check if email exists
    $query = "SELECT id, first_name FROM users WHERE email = :email AND status = 'active'";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':email', $email);
    $stmt->execute();
    
    $user = $stmt->fetch();
    
    if (!$user) {
        // Don't reveal if email exists or not for security
        echo json_encode(['success' => true, 'message' => 'If the email exists, a reset link has been sent']);
        return;
    }
    
    // Generate reset token
    $token = bin2hex(random_bytes(32));
    $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
    
    // Store token (you might want to create a password_resets table)
    // For now, we'll just return success
    
    echo json_encode([
        'success' => true,
        'message' => 'Password reset link has been sent to your email'
    ]);
}

function handleResetPassword($db, $input) {
    $token = $input['token'] ?? '';
    $password = $input['password'] ?? '';
    $confirm_password = $input['confirm_password'] ?? '';
    
    if (empty($token) || empty($password)) {
        echo json_encode(['success' => false, 'message' => 'Token and password are required']);
        return;
    }
    
    if ($password !== $confirm_password) {
        echo json_encode(['success' => false, 'message' => 'Passwords do not match']);
        return;
    }
    
    if (strlen($password) < 6) {
        echo json_encode(['success' => false, 'message' => 'Password must be at least 6 characters']);
        return;
    }
    
    // Verify token and update password
    // Implementation depends on how you store reset tokens
    
    echo json_encode([
        'success' => true,
        'message' => 'Password reset successful',
        'redirect' => BASE_URL . 'login'
    ]);
}

function getDashboardUrl($user_type) {
    switch ($user_type) {
        case 'admin':
            return BASE_URL . 'admin';
        case 'vendor':
            return BASE_URL . 'vendor-dashboard';
        default:
            return BASE_URL . 'dashboard';
    }
}

function logLoginAttempt($db, $email, $success, $ip_address) {
    try {
        $query = "
            INSERT INTO login_attempts (email, success, ip_address, attempted_at)
            VALUES (:email, :success, :ip_address, CURRENT_TIMESTAMP)
        ";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':email', $email);
        $stmt->bindParam(':success', $success, PDO::PARAM_BOOL);
        $stmt->bindParam(':ip_address', $ip_address);
        $stmt->execute();
    } catch (Exception $e) {
        // Silently fail if login_attempts table doesn't exist
        error_log('Login attempt logging failed: ' . $e->getMessage());
    }
}

function storeRememberToken($db, $user_id, $token, $expires) {
    try {
        // First, clean up old tokens for this user
        $cleanup_query = "DELETE FROM remember_tokens WHERE user_id = :user_id OR expires_at < CURRENT_TIMESTAMP";
        $cleanup_stmt = $db->prepare($cleanup_query);
        $cleanup_stmt->bindParam(':user_id', $user_id);
        $cleanup_stmt->execute();

        // Store new token
        $query = "
            INSERT INTO remember_tokens (user_id, token, expires_at)
            VALUES (:user_id, :token, FROM_UNIXTIME(:expires))
        ";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':token', hash('sha256', $token)); // Store hashed token
        $stmt->bindParam(':expires', $expires);
        $stmt->execute();
    } catch (Exception $e) {
        // Silently fail if remember_tokens table doesn't exist
        error_log('Remember token storage failed: ' . $e->getMessage());
    }
}

function checkRememberToken($db) {
    if (!isset($_COOKIE['remember_token'])) {
        return false;
    }

    try {
        $token = $_COOKIE['remember_token'];
        $hashed_token = hash('sha256', $token);

        $query = "
            SELECT rt.user_id, u.*
            FROM remember_tokens rt
            JOIN users u ON rt.user_id = u.id
            WHERE rt.token = :token
            AND rt.expires_at > CURRENT_TIMESTAMP
            AND u.status = 'active'
        ";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':token', $hashed_token);
        $stmt->execute();

        $user = $stmt->fetch();

        if ($user) {
            // Auto-login user
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['first_name'] = $user['first_name'];
            $_SESSION['last_name'] = $user['last_name'];
            $_SESSION['user_type'] = $user['user_type'];
            $_SESSION['auto_login'] = true;

            return true;
        }
    } catch (Exception $e) {
        error_log('Remember token check failed: ' . $e->getMessage());
    }

    return false;
}
?>
